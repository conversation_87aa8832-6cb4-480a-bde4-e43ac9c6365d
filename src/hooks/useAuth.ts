/**
 * @fileoverview Custom React hook for authentication state management.
 * Provides JWT token-based authentication with localStorage persistence.
 * 
 * <AUTHOR> Platform
 * @version 1.0.0
 */

import { useState, useEffect } from 'react';

/**
 * Internal authentication state interface.
 * 
 * @private
 */
interface AuthState {
  /** JWT authentication token or null if not authenticated */
  token: string | null;
  /** Whether the user is currently authenticated */
  isAuthenticated: boolean;
}

/**
 * Custom hook for managing authentication state throughout the application.
 * Handles JWT token storage, retrieval, and state synchronization.
 * Automatically restores authentication state from localStorage on app initialization.
 * 
 * @returns Authentication state and control functions
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   const { isAuthenticated, login, logout } = useAuth();
 *   
 *   if (!isAuthenticated) {
 *     return <LoginForm onLogin={login} />;
 *   }
 *   
 *   return <Dashboard onLogout={logout} />;
 * }
 * ```
 * 
 * @public
 */
export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    token: null,
    isAuthenticated: false
  });

  // Restore authentication state from localStorage on mount
  useEffect(() => {
    const savedToken = localStorage.getItem('authToken');
    if (savedToken) {
      setAuthState({
        token: savedToken,
        isAuthenticated: true
      });
    }
  }, []);

  /**
   * Authenticates the user with a JWT token.
   * Stores the token in localStorage and updates authentication state.
   * 
   * @param token - JWT authentication token
   */
  const login = (token: string) => {
    localStorage.setItem('authToken', token);
    setAuthState({
      token,
      isAuthenticated: true
    });
  };

  /**
   * Logs out the user by clearing the authentication token.
   * Removes token from localStorage and resets authentication state.
   */
  const logout = () => {
    localStorage.removeItem('authToken');
    setAuthState({
      token: null,
      isAuthenticated: false
    });
  };

  return {
    ...authState,
    login,
    logout
  };
};