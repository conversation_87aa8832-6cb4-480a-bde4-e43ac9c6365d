/**
 * @fileoverview Main App component providing authentication flow and routing.
 * Handles conditional rendering between login and authenticated dashboard views.
 *
 * <AUTHOR> Platform
 * @version 1.0.0
 */

import React from "react";
import "./App.css";
import LoginPage from "./components/LoginPage";
import Dashboard from "./components/Dashboard";
import { useAuth } from "./hooks/useAuth";

// Add React Router
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import StandaloneARViewer from "./pages/ARViewerStandalone";

/**
 * Root application component managing authentication flow and routing.
 * Renders either LoginPage for unauthenticated users or Dashboard for authenticated users.
 * Uses the useAuth hook to manage authentication state throughout the application.
 *
 * @returns JSX element containing the appropriate view based on authentication status
 *
 * @public
 */
function App() {
  const { isAuthenticated, login, logout } = useAuth();

  return (
    <Router>
      <Routes>
        <Route path="/ar-viewer/:id" element={<StandaloneARViewer />} />
        <Route
          path="*"
          element={
            <div className="App">
              {!isAuthenticated ? (
                <LoginPage onLogin={login} />
              ) : (
                <Dashboard onLogout={logout} />
              )}
            </div>
          }
        />
      </Routes>
    </Router>
  );
}

export default App;
