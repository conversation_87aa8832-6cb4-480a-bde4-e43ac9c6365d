/**
 * @fileoverview 3D Model Viewer component for previewing available AR models.
 * Allows users to view and interact with 3D models before using them in AR experiences.
 *
 * <AUTHOR> Platform
 * @version 1.0.0
 */

import React, { useState } from "react";
import { ModelType } from "../services/arRenderer";
import "./ModelViewer.css";

/**
 * Interactive 3D model viewer component.
 * Displays available 3D models in an embedded iframe interface.
 * Users can switch between different model types and interact with them directly.
 *
 * Features:
 * - Model selection dropdown
 * - Interactive 3D model preview
 * - Model information display
 * - Support for all available model types (cube, sphere, cone, littletokyo)
 *
 * @returns JSX element containing the model viewer interface
 *
 * @public
 */
export const ModelViewer: React.FC = () => {
  const [selectedModel, setSelectedModel] = useState<ModelType>("cube");

  const availableModels: ModelType[] = [
    "cube",
    "sphere",
    "cone",
    "littletokyo",
    "tictactoe",
  ];

  return (
    <div className="model-viewer">
      <div className="model-viewer-header">
        <h2>3D Model Viewer</h2>
        <div className="model-selector-container">
          <label>Select Model:</label>
          <select
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value as ModelType)}
            className="model-select"
          >
            {availableModels.map((model) => (
              <option key={model} value={model}>
                {model.charAt(0).toUpperCase() + model.slice(1)}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="model-viewer-content">
        <div className="model-iframe-container">
          <iframe
            src={`/models/${selectedModel}.html`}
            className="model-iframe"
            title={`3D ${selectedModel} Model`}
            style={{
              width: "100%",
              height: "100%",
              border: "none",
              borderRadius: "8px",
            }}
          />
        </div>

        <div className="model-info">
          <h3>Model Information</h3>
          <div className="model-details">
            <p>
              <strong>Current Model:</strong> {selectedModel}
            </p>
            <p>
              <strong>Source:</strong> models/{selectedModel}.html
            </p>
            <p>
              <strong>Description:</strong> Interactive 3D {selectedModel} model
              rendered using Three.js
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModelViewer;
