import React, { useRef, useEffect, useState, useCallback, forwardRef, useImperativeHandle } from 'react';
import { ARRenderer, ARModel, ModelType, ARModelConfig, RenderStats, ARSceneConfig, CameraTrackingConfig } from '../services/arRenderer';
import { Pose3D, CameraIntrinsics } from '../types/pose';
import './ARCanvas.css';

// Props interface for ARCanvas component
export interface ARCanvasProps {
  width: number;
  height: number;
  pose?: Pose3D;
  cameraIntrinsics?: CameraIntrinsics;
  selectedModel?: ModelType;
  modelVisible?: boolean;
  modelConfig?: Partial<ARModelConfig>;
  sceneConfig?: Partial<ARSceneConfig>;
  trackingConfig?: Partial<CameraTrackingConfig>;
  showStats?: boolean;
  showDebugInfo?: boolean;
  onModelCreate?: (model: ARModel) => void;
  onModelUpdate?: (model: ARModel) => void;
  onPoseUpdate?: (pose: Pose3D) => void;
  onStatsUpdate?: (stats: RenderStats) => void;
  onRendererReady?: (renderer: ARRenderer) => void;
  onError?: (error: Error) => void;
}

// Ref interface for imperative methods
export interface ARCanvasRef {
  takeScreenshot: () => string | null;
  getRenderer: () => ARRenderer | null;
  getStats: () => RenderStats;
  getCurrentPose: () => Pose3D | null;
}

export const ARCanvas = forwardRef<ARCanvasRef, ARCanvasProps>(({
  width,
  height,
  pose,
  cameraIntrinsics,
  selectedModel = 'cube',
  modelVisible = true,
  modelConfig = {},
  sceneConfig = {},
  trackingConfig = {},
  showStats = false,
  showDebugInfo = false,
  onModelCreate,
  onModelUpdate,
  onPoseUpdate,
  onStatsUpdate,
  onRendererReady,
  onError
}, ref) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const rendererRef = useRef<ARRenderer | null>(null);
  const currentModelRef = useRef<ARModel | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  
  const [isInitialized, setIsInitialized] = useState(false);
  const [renderStats, setRenderStats] = useState<RenderStats>({
    fps: 0,
    frameTime: 0,
    triangles: 0,
    drawCalls: 0,
    memoryUsage: { geometries: 0, textures: 0, materials: 0 }
  });
  const [error, setError] = useState<string>('');

  // Initialize Three.js renderer
  const initializeRenderer = useCallback(() => {
    if (!canvasRef.current) {
      const errorMsg = 'Canvas element not found';
      setError(errorMsg);
      return;
    }

    try {
      // Create AR renderer
      const renderer = new ARRenderer(
        canvasRef.current,
        width,
        height,
        { ...sceneConfig, ...trackingConfig }
      );

      rendererRef.current = renderer;

      // Set camera intrinsics if provided
      if (cameraIntrinsics) {
        renderer.setCameraIntrinsics(cameraIntrinsics);
      }

      // Start rendering
      renderer.startRendering();

      setIsInitialized(true);
      setError('');

      // Notify parent component
      if (onRendererReady) {
        onRendererReady(renderer);
      }

    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to initialize renderer';
      setError(errorMsg);
      if (onError) {
        onError(new Error(errorMsg));
      }
    }
  }, [width, height, cameraIntrinsics, sceneConfig, trackingConfig, onRendererReady, onError]);

  // Create or update 3D model
  const updateModel = useCallback(() => {
    if (!rendererRef.current || !isInitialized) return;

    const modelId = 'arModel';
    
    try {
      // Remove existing model if type changed
      if (currentModelRef.current && currentModelRef.current.type !== selectedModel) {
        rendererRef.current.removeModel(modelId);
        currentModelRef.current = null;
      }

      // Create new model if it doesn't exist
      if (!currentModelRef.current) {
        const config: ARModelConfig = {
          type: selectedModel,
          position: { x: 0, y: 0, z: -2.5 }, // Fixed overlay position
          scale: { x: 1, y: 1, z: 1 }, // Will be dynamically scaled to fit screen
          color: '#00aaff',
          opacity: 0.5, // Semi-transparent for overlay effect
          castShadow: false, // No shadows for overlay
          receiveShadow: false,
          wireframe: false,
          ...modelConfig
        };
        
        const model = rendererRef.current.createModel(modelId, config);
        currentModelRef.current = model;
        
        // Immediately set the correct visibility after creation
        rendererRef.current.updateModel(modelId, { visible: modelVisible });

        if (onModelCreate) {
          onModelCreate(model);
        }
      }

      // Update model visibility and properties  
      rendererRef.current.updateModel(modelId, {
        visible: modelVisible,
        ...modelConfig
      });

      if (onModelUpdate && currentModelRef.current) {
        onModelUpdate(currentModelRef.current);
      }

    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to update model';
      setError(errorMsg);
      if (onError) {
        onError(new Error(errorMsg));
      }
    }
  }, [selectedModel, modelVisible, modelConfig, isInitialized, onModelCreate, onModelUpdate, onError]);

  // Update camera pose from tracking data
  const updateCameraPose = useCallback(() => {
    if (!rendererRef.current || !pose || !isInitialized) {
      return;
    }

    try {
      rendererRef.current.updateCameraPose(pose);
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to update camera pose';
      setError(errorMsg);
      if (onError) {
        onError(new Error(errorMsg));
      }
    }
  }, [pose, isInitialized, onError]);

  // Update performance statistics
  const updateStats = useCallback(() => {
    if (!rendererRef.current || !showStats) return;

    const stats = rendererRef.current.getStats();
    setRenderStats(stats);

    if (onStatsUpdate) {
      onStatsUpdate(stats);
    }
  }, [showStats, onStatsUpdate]);

  // Stats update loop
  const statsLoop = useCallback(() => {
    if (showStats && isInitialized) {
      updateStats();
      animationFrameRef.current = requestAnimationFrame(statsLoop);
    }
  }, [showStats, isInitialized, updateStats]);

  // Initialize renderer on mount
  useEffect(() => {
    initializeRenderer();

    return () => {
      // Cleanup on unmount
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      
      if (rendererRef.current) {
        rendererRef.current.dispose();
        rendererRef.current = null;
      }
      
      setIsInitialized(false);
    };
  }, [initializeRenderer]); // Include initializeRenderer dependency

  // Update renderer size when dimensions change
  useEffect(() => {
    if (rendererRef.current && isInitialized) {
      rendererRef.current.resize(width, height);
    }
  }, [width, height, isInitialized]);

  // Update camera intrinsics when they change
  useEffect(() => {
    if (rendererRef.current && cameraIntrinsics && isInitialized) {
      rendererRef.current.setCameraIntrinsics(cameraIntrinsics);
    }
  }, [cameraIntrinsics, isInitialized]);

  // Update model when configuration changes
  useEffect(() => {
    if (isInitialized) {
      updateModel();
    }
  }, [selectedModel, modelVisible, modelConfig, isInitialized, updateModel]);

  // Update camera pose when pose data changes
  useEffect(() => {
    if (isInitialized) {
      updateCameraPose();
    }
  }, [pose, isInitialized, updateCameraPose]);

  // Start/stop stats loop
  useEffect(() => {
    if (showStats && isInitialized) {
      statsLoop();
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  }, [showStats, isInitialized, statsLoop]);

  // Handle canvas click for debugging
  const handleCanvasClick = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!showDebugInfo || !rendererRef.current) return;

    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Debug info available in development mode
  }, [showDebugInfo, isInitialized, error]);

  // Take screenshot
  const takeScreenshot = useCallback((): string | null => {
    if (!rendererRef.current || !isInitialized) return null;
    return rendererRef.current.takeScreenshot();
  }, [isInitialized]);

  // Get current renderer instance
  const getRenderer = useCallback((): ARRenderer | null => {
    return rendererRef.current;
  }, []);

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    takeScreenshot,
    getRenderer,
    getStats: () => renderStats,
    getCurrentPose: () => rendererRef.current?.getCurrentPose() || null
  }), [takeScreenshot, getRenderer, renderStats]);

  return (
    <div className="ar-canvas-container">
      <canvas
        ref={canvasRef}
        className="ar-canvas"
        width={width}
        height={height}
        style={{ width, height }}
        onClick={handleCanvasClick}
      />
      
      {error && (
        <div className="ar-canvas-error">
          <span>AR Error: {error}</span>
        </div>
      )}
      
      {showStats && isInitialized && (
        <div className="ar-canvas-stats">
          <div className="stats-section">
            <h4>Performance</h4>
            <div className="stats-grid">
              <span>FPS:</span><span>{renderStats.fps}</span>
              <span>Frame:</span><span>{renderStats.frameTime.toFixed(1)}ms</span>
              <span>Triangles:</span><span>{renderStats.triangles.toLocaleString()}</span>
              <span>Calls:</span><span>{renderStats.drawCalls}</span>
            </div>
          </div>
          
          <div className="stats-section">
            <h4>Memory</h4>
            <div className="stats-grid">
              <span>Geometries:</span><span>{renderStats.memoryUsage.geometries}</span>
              <span>Textures:</span><span>{renderStats.memoryUsage.textures}</span>
              <span>Materials:</span><span>{renderStats.memoryUsage.materials}</span>
            </div>
          </div>
          
          {pose && (
            <div className="stats-section">
              <h4>Pose</h4>
              <div className="stats-grid">
                <span>X:</span><span>{pose.translation.x.toFixed(3)}</span>
                <span>Y:</span><span>{pose.translation.y.toFixed(3)}</span>
                <span>Z:</span><span>{pose.translation.z.toFixed(3)}</span>
                <span>Conf:</span><span>{(pose.confidence * 100).toFixed(1)}%</span>
              </div>
            </div>
          )}
        </div>
      )}
      
      {showDebugInfo && isInitialized && (
        <div className="ar-canvas-debug">
          <div className="debug-info">
            <span>Click canvas for debug info</span>
            <span>Model: {selectedModel}</span>
            <span>Visible: {modelVisible ? 'Yes' : 'No'}</span>
            <span>Initialized: {isInitialized ? 'Yes' : 'No'}</span>
          </div>
        </div>
      )}
      
      {!isInitialized && !error && (
        <div className="ar-canvas-loading">
          <div className="loading-spinner"></div>
          <span>Initializing AR Renderer...</span>
        </div>
      )}
    </div>
  );
});

ARCanvas.displayName = 'ARCanvas';

export default ARCanvas;