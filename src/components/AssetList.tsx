import React, { useState, useEffect, useCallback } from "react";
import {
  Asset,
  AssetListResponse,
  apiService,
} from "../services/api";
import AssetUpload from "./AssetUpload";
import "./AssetList.css";

const AssetList: React.FC = () => {
  const [assets, setAssets] = useState<Asset[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>("");
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    size: 12,
  });
  const [showUpload, setShowUpload] = useState<boolean>(false);
  const [models, setModels] = useState<string[]>([]);
  const [modelsLoading, setModelsLoading] = useState<boolean>(false);
  const [assetModels, setAssetModels] = useState<{[key: string]: string}>({});
  const [updatingModels, setUpdatingModels] = useState<{[key: string]: boolean}>({});

  const fetchModels = async () => {
    try {
      setModelsLoading(true);
      const data = await apiService.getModels();
      setModels(data);
    } catch (err) {
      // Silently handle error - models will use fallback
    } finally {
      setModelsLoading(false);
    }
  };

  const fetchAssets = useCallback(async () => {
    try {
      setLoading(true);
      setError("");
      const data: AssetListResponse = await apiService.getAssets(
        pagination.page,
        pagination.size,
      );
      setAssets(data.assets);
      setPagination({
        total: data.total,
        page: data.page,
        size: data.size,
      });
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to fetch assets",
      );
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.size]);

  useEffect(() => {
    fetchAssets();
  }, [fetchAssets]);

  useEffect(() => {
    fetchModels();
  }, []);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };


  const handleCopyUrl = (url: string) => {
    navigator.clipboard.writeText(url);
    // Could add a toast notification here
  };

  const handleDeleteAsset = async (asset: Asset) => {
    if (window.confirm(`Are you sure you want to delete "${asset.filename}"?`)) {
      try {
        await apiService.deleteAsset(asset._id);
        await fetchAssets();
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to delete asset");
      }
    }
  };

  const handleModelChange = (assetId: string, modelName: string) => {
    setAssetModels(prev => ({
      ...prev,
      [assetId]: modelName
    }));
  };

  const handleUpdateModel = async (assetId: string) => {
    const selectedModel = assetModels[assetId];
    if (!selectedModel) {
      setError("Please select a model before updating");
      return;
    }

    try {
      setUpdatingModels(prev => ({ ...prev, [assetId]: true }));
      setError("");
      
      await apiService.updateAssetModel(assetId, selectedModel);
      
      // Refresh the assets list to show updated data
      await fetchAssets();
      
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to update asset model"
      );
    } finally {
      setUpdatingModels(prev => ({ ...prev, [assetId]: false }));
    }
  };


  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const handleUploadSuccess = () => {
    setShowUpload(false);
    fetchAssets();
  };

  const totalPages = Math.ceil(pagination.total / pagination.size);

  if (loading) {
    return <div className="asset-loading">Loading assets...</div>;
  }

  if (error) {
    return <div className="asset-error">Error: {error}</div>;
  }

  return (
    <div className="asset-list">
      <div className="asset-header-section">
        <h2>Assets</h2>
        <div className="asset-actions">
          <span className="asset-stats">Total: {pagination.total} assets</span>
          <button onClick={() => setShowUpload(true)} className="upload-button">
            Upload Asset
          </button>
        </div>
      </div>

      {assets.length === 0 ? (
        <div className="asset-empty">No assets found.</div>
      ) : (
        <>
          <div className="asset-table-container">
            <table className="asset-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Filename</th>
                  <th>Type</th>
                  <th>Size</th>
                  <th>Campaign</th>
                  <th>Model</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {assets.map((asset) => (
                  <tr key={asset._id}>
                    <td 
                      className="asset-id" 
                      onClick={() => navigator.clipboard.writeText(asset._id)} 
                      title="Click to copy ID" 
                      style={{cursor: 'pointer', fontFamily: 'monospace'}}
                    >
                      {asset._id}
                    </td>
                    <td className="asset-filename">{asset.filename}</td>
                    <td className="asset-type">{asset.mime_type}</td>
                    <td className="asset-size">{formatFileSize(asset.file_size)}</td>
                    <td className="asset-campaign">{asset.campaign_id}</td>
                    <td className="asset-model">
                      <div className="asset-model-container">
                        <select 
                          value={assetModels[asset._id] || ""}
                          onChange={(e) => handleModelChange(asset._id, e.target.value)}
                          className="asset-model-dropdown"
                          disabled={modelsLoading || updatingModels[asset._id]}
                        >
                          <option value="">Select Model</option>
                          {models.map(model => (
                            <option key={model} value={model}>{model}</option>
                          ))}
                        </select>
                        <button
                          onClick={() => handleUpdateModel(asset._id)}
                          disabled={!assetModels[asset._id] || updatingModels[asset._id]}
                          className="update-model-button"
                          title="Update Model"
                        >
                          {updatingModels[asset._id] ? "Updating..." : "Update"}
                        </button>
                      </div>
                    </td>
                    <td>
                      <div className="asset-actions">
                        <button
                          onClick={() => handleCopyUrl(asset.url)}
                          className="copy-url-button"
                          title="Copy URL"
                        >
                          Copy URL
                        </button>
                        <button
                          onClick={() => handleDeleteAsset(asset)}
                          className="delete-asset-button"
                          title="Delete"
                        >
                          🗑️
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {totalPages > 1 && (
            <div className="pagination">
              <button 
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1}
                className="pagination-button"
              >
                Previous
              </button>
              
              <span className="pagination-info">
                Page {pagination.page} of {totalPages}
              </span>
              
              <button 
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page === totalPages}
                className="pagination-button"
              >
                Next
              </button>
            </div>
          )}
        </>
      )}

      {showUpload && (
        <AssetUpload
          onSuccess={handleUploadSuccess}
          onCancel={() => setShowUpload(false)}
        />
      )}
    </div>
  );
};

export default AssetList;