import React, { useState } from 'react';
import CampaignList from './CampaignList';
import { ARViewer } from './ARViewer';
import { Campaign } from '../services/api';

const Campaigns: React.FC = () => {
  const [showARViewer, setShowARViewer] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null);

  const handleOpenARViewer = (campaign: Campaign) => {
    setSelectedCampaign(campaign);
    setShowARViewer(true);
  };

  const handleCloseARViewer = () => {
    setShowARViewer(false);
    setSelectedCampaign(null);
  };

  if (showARViewer && selectedCampaign) {
    return (
      <div className="campaigns">
        <div className="ar-viewer-header-bar">
          <h3>AR Viewer - {selectedCampaign.name}</h3>
          <button onClick={handleCloseARViewer} className="close-ar-button">
            ← Back to Campaigns
          </button>
        </div>
        <ARViewer campaignId={selectedCampaign._id} />
      </div>
    );
  }

  return (
    <div className="campaigns">
      <CampaignList onOpenARViewer={handleOpenARViewer} />
    </div>
  );
};

export default Campaigns;

// Add styles for AR viewer header bar
const styles = `
.ar-viewer-header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #282c34;
  color: white;
  padding: 1rem 2rem;
  border-bottom: 1px solid #404040;
}

.ar-viewer-header-bar h3 {
  margin: 0;
  font-size: 1.2rem;
}

.close-ar-button {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s ease;
}

.close-ar-button:hover {
  background-color: #5a6268;
}
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);
}