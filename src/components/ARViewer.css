.ar-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
  color: white;
}

.ar-viewer-header {
  background-color: #282c34;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #404040;
}

.ar-viewer-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: white;
}

.ar-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
}

.matched-model-info {
  margin-top: 0.25rem;
  font-size: 0.8rem;
  color: #4CAF50;
  font-style: italic;
}

.model-selector:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.auto-capture-btn.found {
  background-color: #4CAF50;
  color: white;
}

.auto-capture-btn.found:hover {
  background-color: #45a049;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.control-group label {
  font-size: 0.8rem;
  color: #ccc;
  font-weight: 500;
}

.button-group {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.color-selector {
  background-color: #404040;
  color: white;
  border: 1px solid #606060;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
}

.color-selector:focus {
  outline: none;
  border-color: #007bff;
}

.model-selector {
  background-color: #404040;
  color: white;
  border: 1px solid #606060;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
}

.model-selector:focus {
  outline: none;
  border-color: #007bff;
}

.toggle-model-btn {
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s ease;
}

.toggle-model-btn:hover {
  background-color: #218838;
}

.toggle-model-btn.active {
  background-color: #dc3545;
}

.toggle-model-btn.active:hover {
  background-color: #c82333;
}

.capture-btn {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s ease;
}

.capture-btn:hover {
  background-color: #0056b3;
}

.auto-capture-btn {
  background-color: #17a2b8;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s ease;
}

.auto-capture-btn:hover {
  background-color: #138496;
}

.auto-capture-btn.active {
  background-color: #dc3545;
}

.auto-capture-btn.active:hover {
  background-color: #c82333;
}

.pose-overlay-btn {
  background-color: #6f42c1;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s ease;
}

.pose-overlay-btn:hover {
  background-color: #5a2d91;
}

.pose-overlay-btn.active {
  background-color: #28a745;
}

.pose-overlay-btn.active:hover {
  background-color: #218838;
}

.ar-canvas-btn {
  background-color: #fd7e14;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s ease;
}

.ar-canvas-btn:hover {
  background-color: #e8590c;
}

.ar-canvas-btn.active {
  background-color: #28a745;
}

.ar-canvas-btn.active:hover {
  background-color: #218838;
}

.stats-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s ease;
}

.stats-btn:hover {
  background-color: #5a6268;
}

.stats-btn.active {
  background-color: #28a745;
}

.stats-btn.active:hover {
  background-color: #218838;
}

.ar-viewer-unauthorized {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #666;
}

.ar-error {
  background-color: #dc3545;
  color: white;
  padding: 1rem 2rem;
  text-align: center;
  border-bottom: 1px solid #bd2130;
}

.ar-viewport {
  flex: 1;
  position: relative;
  background-color: #000;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ar-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: #000;
}

.ar-canvas {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.ar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.ar-model-container {
  position: absolute;
  border-radius: 12px;
  pointer-events: none;
  transform-origin: center;
  z-index: 15;
}

.ar-model-container.webgl {
  /* WebGL AR overlay with glow effect */
  box-shadow: 0 0 30px rgba(0, 170, 255, 0.4);
  border: 2px solid rgba(0, 170, 255, 0.3);
  background: rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(1px);
}

.ar-model-container.fallback {
  /* Fallback overlay style */
  border: 2px solid rgba(255, 255, 255, 0.4);
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(2px);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
}

.ar-model-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 10px;
  background: transparent;
}

.ar-status {
  background-color: #343a40;
  padding: 1rem 2rem;
  border-top: 1px solid #495057;
}

.status-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 0.75rem;
}

.ar-session-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.session-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  color: #adb5bd;
  flex-wrap: wrap;
}

.pose-info {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  color: #ffc107;
  flex-wrap: wrap;
}

.session-stats span,
.pose-info span {
  white-space: nowrap;
}

.debug-info {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  color: #17a2b8;
  flex-wrap: wrap;
  font-style: italic;
}

.status-indicator {
  font-size: 0.9rem;
  font-weight: 500;
}

.status-indicator.streaming {
  color: #28a745;
}

.status-indicator.streaming::before {
  content: '●';
  margin-right: 0.5rem;
  animation: pulse 2s infinite;
}

.status-indicator.stopped {
  color: #dc3545;
}

.status-indicator.stopped::before {
  content: '●';
  margin-right: 0.5rem;
}

.status-indicator.capturing {
  color: #ffc107;
}

.status-indicator.capturing::before {
  content: '●';
  margin-right: 0.5rem;
  animation: pulse 1s infinite;
}

.status-indicator.idle {
  color: #6c757d;
}

.status-indicator.idle::before {
  content: '●';
  margin-right: 0.5rem;
}

.status-indicator.ar-excellent {
  color: #28a745;
}

.status-indicator.ar-excellent::before {
  content: '●';
  margin-right: 0.5rem;
  animation: pulse 2s infinite;
}

.status-indicator.ar-good {
  color: #17a2b8;
}

.status-indicator.ar-good::before {
  content: '●';
  margin-right: 0.5rem;
  animation: pulse 2s infinite;
}

.status-indicator.ar-fair {
  color: #ffc107;
}

.status-indicator.ar-fair::before {
  content: '●';
  margin-right: 0.5rem;
  animation: pulse 2s infinite;
}

.status-indicator.ar-poor {
  color: #fd7e14;
}

.status-indicator.ar-poor::before {
  content: '●';
  margin-right: 0.5rem;
  animation: pulse 1.5s infinite;
}

.status-indicator.ar-lost {
  color: #dc3545;
}

.status-indicator.ar-lost::before {
  content: '●';
  margin-right: 0.5rem;
}

.match-result {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
}

.match-result.found {
  background-color: rgba(40, 167, 69, 0.2);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.5);
}

.match-result.not-found {
  background-color: rgba(220, 53, 69, 0.2);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.5);
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .ar-viewer-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .ar-controls {
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.75rem;
  }
  
  .control-group {
    min-width: 120px;
  }
  
  .button-group {
    gap: 0.25rem;
  }
  
  .ar-model-container {
    width: 150px;
    height: 150px;
  }
  
  .status-row {
    gap: 1rem;
  }
  
  .session-stats,
  .pose-info {
    gap: 0.5rem;
  }
}