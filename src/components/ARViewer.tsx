import React, { useRef, useEffect, useState, useCallback } from "react";
import { useAuth } from "../hooks/useAuth";
import { FrameCaptureService, MatchResult } from "../services/frameCapture";
import { Pose3D, CameraIntrinsics, PoseKeypoints } from "../types/pose";
import { poseEstimationService } from "../services/poseEstimation";
import { ModelType, ARModelConfig, ARUtils } from "../services/arRenderer";
import "./ARViewer.css";

/**
 * Props interface for the ARViewer component.
 *
 * @public
 */
interface ARViewerProps {
  /** ID of the campaign to use for asset matching */
  campaignId: string;
  /** Currently selected 3D model type */
  selectedModel?: ModelType;
  /** Callback function when user changes the selected model */
  onModelChange?: (model: ModelType) => void;
}

/**
 * Internal state interface for AR session management.
 * Tracks the current status of the AR tracking and rendering system.
 *
 * @private
 */
interface ARSessionState {
  /** Whether the AR system has been fully initialized */
  isInitialized: boolean;
  /** Whether pose tracking is currently active */
  isTracking: boolean;
  /** Whether an asset has been successfully matched */
  hasMatchedAsset: boolean;
  /** Current quality level of pose tracking */
  trackingQuality: "excellent" | "good" | "fair" | "poor" | "lost";
  /** Timestamp of the last tracking update */
  lastTrackingUpdate: number;
}

/**
 * Main AR viewer component providing real-time augmented reality experiences.
 * Integrates camera feed, pose estimation, asset matching, and 3D model rendering.
 *
 * Features:
 * - Real-time camera feed with pose estimation
 * - Automatic asset matching against campaign database
 * - Interactive 3D model overlays
 * - Auto-toggle system based on pose detection
 * - Support for multiple model types (cube, sphere, cone, littletokyo)
 *
 * @param props - Component configuration options
 * @returns JSX element containing the full AR interface
 *
 * @example
 * ```tsx
 * <ARViewer
 *   campaignId="campaign-123"
 *   selectedModel="cube"
 *   onModelChange={(model) => setSelectedModel(model)}
 * />
 * ```
 *
 * @public
 */
export const ARViewer: React.FC<ARViewerProps> = ({
  campaignId,
  selectedModel = "cube",
  onModelChange,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const overlayRef = useRef<HTMLDivElement>(null);
  const frameCaptureRef = useRef<FrameCaptureService | null>(null);
  const { isAuthenticated } = useAuth();

  // Camera and streaming state
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string>("");

  // AR session state
  const [arSession, setArSession] = useState<ARSessionState>({
    isInitialized: false,
    isTracking: false,
    hasMatchedAsset: false,
    trackingQuality: "lost",
    lastTrackingUpdate: 0,
  });

  // Continuous tracking interval
  const trackingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const predictionIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const isProcessingPose = useRef(false); // Prevent processing backup
  const lastPose = useRef<Pose3D | null>(null);
  const poseVelocity = useRef<{ x: number; y: number; z: number }>({
    x: 0,
    y: 0,
    z: 0,
  });
  const processingTimes = useRef<number[]>([]); // Track performance
  const lastValidPoseTime = useRef<number>(0); // Track when we last had a valid pose

  // Model and rendering state
  const [modelVisible, setModelVisible] = useState(false);
  const [modelConfig, setModelConfig] = useState<Partial<ARModelConfig>>({
    color: "#00aaff",
    opacity: 0.8,
    scale: { x: 0.5, y: 0.5, z: 0.5 },
  });

  // Capture and tracking state
  const [isCapturing, setIsCapturing] = useState(false);
  const [lastMatch, setLastMatch] = useState<MatchResult | null>(null);
  const [matchedModel, setMatchedModel] = useState<ModelType | null>(null);
  const [assetFound, setAssetFound] = useState(false);
  const [captureStats, setCaptureStats] = useState({
    framesProcessed: 0,
    matchesFound: 0,
  });
  const [referenceKeypoints, setReferenceKeypoints] =
    useState<PoseKeypoints | null>(null);

  // Use matched model if available, otherwise fall back to selected model
  const activeModel = matchedModel || selectedModel;

  // UI state
  const [showARCanvas, setShowARCanvas] = useState(true);
  const [showStats, setShowStats] = useState(false);

  // Debug state
  const [debugInfo, setDebugInfo] = useState("");

  // Camera intrinsics for AR
  const [cameraIntrinsics, setCameraIntrinsics] =
    useState<CameraIntrinsics | null>(null);
  const cameraIntrinsicsRef = useRef<CameraIntrinsics | null>(null);

  const availableModels: ModelType[] = ARUtils.getAvailableModelTypes();

  useEffect(() => {
    console.log("Model found: ", matchedModel)
  }, [matchedModel]);
  useEffect(() => {
    console.log({ debugInfo });
  }, [debugInfo]);
  useEffect(() => {
    if (!isAuthenticated) return;

    setDebugInfo("Initializing AR system...");

    // Set default camera intrinsics immediately
    const defaultIntrinsics = ARUtils.createDefaultIntrinsics(640, 480);
    setCameraIntrinsics(defaultIntrinsics);
    cameraIntrinsicsRef.current = defaultIntrinsics;

    startCamera();
    initFrameCapture();
    initPoseEstimation();

    return () => {
      stopCamera();
      cleanupFrameCapture();
      stopContinuousTracking();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated]);

  const initPoseEstimation = async () => {
    try {
      await poseEstimationService.initialize();

      // Initialize camera intrinsics with default values
      if (videoRef.current) {
        const width = videoRef.current.videoWidth || 640;
        const height = videoRef.current.videoHeight || 480;
        const intrinsics = ARUtils.createDefaultIntrinsics(width, height);
        setCameraIntrinsics(intrinsics);
      }
    } catch (error) {
      console.error("Failed to initialize pose estimation:", error);
      setError("Failed to initialize pose estimation");
    }
  };

  // Update AR session state
  const updateARSession = useCallback((updates: Partial<ARSessionState>) => {
    setArSession((prev) => ({
      ...prev,
      ...updates,
      lastTrackingUpdate: Date.now(),
    }));
  }, []);

  // Determine tracking quality from pose confidence
  const getTrackingQuality = useCallback(
    (pose: Pose3D | null): ARSessionState["trackingQuality"] => {
      if (!pose || pose.confidence < 0.2) return "lost"; // More permissive threshold
      if (pose.confidence > 0.7) return "excellent";
      if (pose.confidence > 0.5) return "good";
      if (pose.confidence > 0.3) return "fair";
      return "poor";
    },
    [],
  );

  // Start continuous tracking after initial match
  const startContinuousTracking = useCallback(() => {
    if (trackingIntervalRef.current) {
      return;
    }

    trackingIntervalRef.current = setInterval(async () => {
      // Skip frame if still processing previous frame
      if (isProcessingPose.current) {
        return;
      }

      if (
        arSession.hasMatchedAsset &&
        videoRef.current &&
        !isCapturing &&
        referenceKeypoints
      ) {
        isProcessingPose.current = true;
        const startTime = performance.now();

        try {
          // Detect new keypoints from current frame but use cached reference keypoints
          const currentKeypoints = await poseEstimationService.detectKeypoints(
            videoRef.current,
          );
          const poseAnalysis =
            await poseEstimationService.analyzePoseFromKeypoints(
              currentKeypoints,
              referenceKeypoints,
              cameraIntrinsics || undefined,
            );

          // Track processing time for adaptive quality
          const processingTime = performance.now() - startTime;
          processingTimes.current.push(processingTime);
          if (processingTimes.current.length > 10) {
            processingTimes.current.shift(); // Keep last 10 measurements
          }
          if (poseAnalysis.pose && poseAnalysis.match_result?.matched) {
            // Calculate velocity for prediction
            if (lastPose.current) {
              poseVelocity.current = {
                x:
                  (poseAnalysis.pose.translation.x -
                    lastPose.current.translation.x) *
                  12, // Scale for 83ms interval
                y:
                  (poseAnalysis.pose.translation.y -
                    lastPose.current.translation.y) *
                  12,
                z:
                  (poseAnalysis.pose.translation.z -
                    lastPose.current.translation.z) *
                  12,
              };
            }

            lastPose.current = poseAnalysis.pose;
            const quality = getTrackingQuality(poseAnalysis.pose);
            updateARSession({
              isTracking: quality !== "lost",
              trackingQuality: quality,
            });

            // Update last valid pose time and auto-toggle 3D model ON
            lastValidPoseTime.current = Date.now();
            // Auto-enable required components for 3D model display
            setShowARCanvas(true);
            setModelVisible(true);
          } else {
            // No valid pose - either no object detected or poor match
            // Keep model visible - let the prediction interval handle hiding after timeout

            lastPose.current = null;
            poseVelocity.current = { x: 0, y: 0, z: 0 };
          }
        } catch (error) {
          console.error("Continuous pose tracking error:", error);
        } finally {
          isProcessingPose.current = false;
        }
      }
    }, 83); // 12 FPS for actual pose estimation - better balance

    // Start ultra-high-frequency prediction for silky smooth visuals
    predictionIntervalRef.current = setInterval(() => {
      if (lastPose.current && arSession.hasMatchedAsset) {
        const now = Date.now();
        const timeSinceLastValidPose = now - lastValidPoseTime.current;

        // Auto-toggle model OFF if no valid pose for more than 2 seconds (less aggressive)
        if (timeSinceLastValidPose > 2000 && modelVisible) {
          setModelVisible(false);
          return;
        }

        // Only predict if we had a recent valid pose
        if (timeSinceLastValidPose < 1000) {
          // Predict next pose based on velocity with damping
          const damping = 0.95; // Reduce velocity over time
          poseVelocity.current = {
            x: poseVelocity.current.x * damping,
            y: poseVelocity.current.y * damping,
            z: poseVelocity.current.z * damping,
          };

          const predictedPose: Pose3D = {
            ...lastPose.current,
            translation: {
              x:
                lastPose.current.translation.x + poseVelocity.current.x * 0.008, // 120fps prediction
              y:
                lastPose.current.translation.y + poseVelocity.current.y * 0.008,
              z:
                lastPose.current.translation.z + poseVelocity.current.z * 0.008,
            },
          };
          lastPose.current = predictedPose; // Update last pose for next prediction
        }
      }
    }, 8); // 120 FPS prediction for ultra-smooth visuals
  }, [
    arSession.hasMatchedAsset,
    isCapturing,
    getTrackingQuality,
    updateARSession,
    referenceKeypoints,
    cameraIntrinsics,
    modelVisible,
  ]);

  // Stop continuous tracking
  const stopContinuousTracking = useCallback(() => {
    if (trackingIntervalRef.current) {
      clearInterval(trackingIntervalRef.current);
      trackingIntervalRef.current = null;
    }
    if (predictionIntervalRef.current) {
      clearInterval(predictionIntervalRef.current);
      predictionIntervalRef.current = null;
    }
    // Reset prediction state
    lastPose.current = null;
    poseVelocity.current = { x: 0, y: 0, z: 0 };
    lastValidPoseTime.current = 0;
  }, []);

  const initFrameCapture = () => {
    if (!frameCaptureRef.current) {
      frameCaptureRef.current = new FrameCaptureService({
        width: 640,
        height: 480,
        quality: 0.8,
        captureRate: 10,
      });

      frameCaptureRef.current.onMatch(async (result: MatchResult) => {
        setLastMatch(result);
        const stats = frameCaptureRef.current?.getStats();
        if (stats) {
          setCaptureStats({
            framesProcessed: stats.framesProcessed,
            matchesFound: stats.matchesFound,
          });
        }

        // Set matched model from asset if available
        if (result.result.matched && result.result.asset?.model) {
          setMatchedModel(result.result.asset.model as ModelType);
          setAssetFound(true);

          // Stop auto capture once asset is found
          if (frameCaptureRef.current) {
            frameCaptureRef.current.stopCapture();
            setIsCapturing(false);
          }
        } else {
          setMatchedModel(null);
          setAssetFound(false);
        }

        // Perform pose analysis when match is found
        if (result.result.matched && videoRef.current) {
          // First, immediately set hasMatchedAsset to true
          updateARSession({
            hasMatchedAsset: true,
          });

          try {
            // Extract and store reference keypoints from matched asset
            let keypointsForAnalysis: PoseKeypoints | undefined = undefined;

            if (
              result.result.asset?.pose_keypoints &&
              result.result.asset?.pose_descriptors
            ) {
              // Transform API response to PoseKeypoints format
              const transformedKeypoints: PoseKeypoints = {
                keypoints: result.result.asset.pose_keypoints.map(
                  (kp: any) => ({
                    x: kp.x,
                    y: kp.y,
                    angle: kp.angle || 0,
                    size: kp.size || 1,
                    response: kp.response || kp.confidence || 1,
                    octave: kp.octave || 0,
                    class_id: kp.class_id || 0,
                  }),
                ),
                descriptors: result.result.asset.pose_descriptors.map(
                  (desc: number[]) => ({
                    data: desc,
                    length: desc.length,
                  }),
                ),
                image_width: result.result.asset.image_width || 0,
                image_height: result.result.asset.image_height || 0,
                detection_confidence: 0.95, // Default confidence
                feature_count: result.result.asset.pose_keypoints.length,
              };
              setReferenceKeypoints(transformedKeypoints);
              keypointsForAnalysis = transformedKeypoints;
            }

            // Detect keypoints from current frame only once
            const currentKeypoints =
              await poseEstimationService.detectKeypoints(videoRef.current);

            // Create pose analysis using cached keypoints instead of re-detecting
            const poseAnalysis =
              await poseEstimationService.analyzePoseFromKeypoints(
                currentKeypoints,
                keypointsForAnalysis || referenceKeypoints || undefined,
                cameraIntrinsicsRef.current || undefined,
              );
            if (poseAnalysis.pose) {
              // Update AR session state - ensure hasMatchedAsset stays true
              const quality = getTrackingQuality(poseAnalysis.pose);
              updateARSession({
                isTracking: true,
                hasMatchedAsset: true,
                trackingQuality: quality,
              });

              // Auto-enable AR Canvas when tracking starts
              setShowARCanvas(true);
              // Don't auto-enable model here - let continuous tracking handle it
            } else {
              // Even if no pose found, keep hasMatchedAsset true since asset was matched
              updateARSession({
                isTracking: false,
                hasMatchedAsset: true,
                trackingQuality: "lost",
              });
            }
          } catch (error) {
            console.error("Pose analysis failed:", error);
            // Even on error, keep hasMatchedAsset true since asset was matched
            updateARSession({
              isTracking: false,
              hasMatchedAsset: true,
              trackingQuality: "lost",
            });
          }
        }
      });

      frameCaptureRef.current.onError((error: Error) => {
        setError(`Frame capture error: ${error.message}`);
        console.error("Frame capture error:", error);
      });
    }
  };

  useEffect(() => {
    toggleAutoCapture();
  }, []);

  // Effect to manage continuous tracking
  useEffect(() => {
    if (arSession.hasMatchedAsset && !isCapturing) {
      startContinuousTracking();
    } else {
      stopContinuousTracking();
    }
  }, [
    arSession.hasMatchedAsset,
    isCapturing,
    startContinuousTracking,
    stopContinuousTracking,
  ]);

  const cleanupFrameCapture = () => {
    if (frameCaptureRef.current) {
      frameCaptureRef.current.destroy();
      frameCaptureRef.current = null;
    }
  };

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: 640,
          height: 480,
          facingMode: "environment",
        },
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setIsStreaming(true);
        setError("");

        // Set video element for frame capture service
        if (frameCaptureRef.current) {
          frameCaptureRef.current.setVideoElement(videoRef.current);
        }

        // Update camera intrinsics when video is ready
        videoRef.current.addEventListener("loadedmetadata", () => {
          if (videoRef.current) {
            const width = videoRef.current.videoWidth;
            const height = videoRef.current.videoHeight;
            const intrinsics = ARUtils.createDefaultIntrinsics(width, height);
            setCameraIntrinsics(intrinsics);
            cameraIntrinsicsRef.current = intrinsics;
          }
        });
      }
    } catch (err) {
      setError("Failed to access camera");
      console.error("Camera error:", err);
    }
  };

  const stopCamera = () => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach((track) => track.stop());
      videoRef.current.srcObject = null;
    }
    setIsStreaming(false);
  };

  const captureFrame = async () => {
    if (!frameCaptureRef.current || !campaignId) {
      setError("Frame capture service not initialized");
      return;
    }

    try {
      const result = await frameCaptureRef.current.captureAndMatch(campaignId);
      if (result) {
        setLastMatch(result);
      }
    } catch (error) {
      setError(
        `Capture failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  };

  const toggleAutoCapture = () => {
    if (!frameCaptureRef.current || !campaignId) {
      setError("Frame capture service not initialized");
      return;
    }

    if (isCapturing) {
      frameCaptureRef.current.stopCapture();
      setIsCapturing(false);
    } else {
      // Reset asset found state when starting new search
      setAssetFound(false);
      setMatchedModel(null);
      setReferenceKeypoints(null);

      // Auto-toggle 3D model OFF when starting new search
      setModelVisible(false);

      // Auto-enable required components for tracking
      setShowARCanvas(true);

      // Reset timing and prediction state
      lastValidPoseTime.current = 0;
      lastPose.current = null;
      poseVelocity.current = { x: 0, y: 0, z: 0 };

      // Reset AR session state for new search
      updateARSession({
        hasMatchedAsset: false,
        isTracking: false,
        trackingQuality: "lost",
      });

      frameCaptureRef.current.startAutoMatching(campaignId);
      setIsCapturing(true);
    }
  };

  const toggleModel = () => {
    const newVisibility = !modelVisible;

    if (newVisibility) {
      // When enabling 3D model, auto-enable required components
      setShowARCanvas(true);
    }

    setModelVisible(newVisibility);
  };

  const handleModelChange = (model: ModelType) => {
    onModelChange?.(model);
  };

  const toggleStats = () => {
    setShowStats(!showStats);
  };

  // Handle model configuration changes
  const updateModelConfig = useCallback((config: Partial<ARModelConfig>) => {
    setModelConfig((prev) => ({ ...prev, ...config }));
  }, []);

  // Model color presets
  const modelColors = [
    { name: "Blue", value: "#00aaff" },
    { name: "Red", value: "#ff4444" },
    { name: "Green", value: "#44ff44" },
    { name: "Purple", value: "#aa44ff" },
    { name: "Orange", value: "#ff8844" },
    { name: "Yellow", value: "#ffff44" },
  ];

  if (!isAuthenticated) {
    return (
      <div className="ar-viewer-unauthorized">
        <p>Please log in to access AR features</p>
      </div>
    );
  }

  return (
    <div className="ar-viewer">
       <div className="ar-viewer-header">
        <h2>AR Viewer</h2>
        <div className="ar-controls">
          <div className="control-group">
            <label>3D Model:</label>
            <select
              value={selectedModel}
              onChange={(e) => handleModelChange(e.target.value as ModelType)}
              className="model-selector"
              disabled={matchedModel !== null}
            >
              {availableModels.map((model) => (
                <option key={model} value={model}>
                  {model.charAt(0).toUpperCase() + model.slice(1)}
                </option>
              ))}
            </select>
            {matchedModel && (
              <div className="matched-model-info">
                Using matched model: <strong>{matchedModel}</strong>
              </div>
            )}
          </div>

          <div className="control-group">
            <label>Color:</label>
            <select
              value={modelConfig.color || "#00aaff"}
              onChange={(e) => updateModelConfig({ color: e.target.value })}
              className="color-selector"
            >
              {modelColors.map((color) => (
                <option key={color.value} value={color.value}>
                  {color.name}
                </option>
              ))}
            </select>
          </div>

          <div className="button-group">
            <button
              onClick={toggleModel}
              className={`toggle-model-btn ${modelVisible ? "active" : ""}`}
              title={modelVisible ? "3D model visible (auto-toggled)" : "3D model hidden (auto-toggled)"}
            >
              {modelVisible ? "🎯 3D ON" : "⚪ 3D OFF"}
            </button>
            <button
              onClick={toggleAutoCapture}
              className={`auto-capture-btn ${isCapturing ? "active" : ""} ${assetFound ? "found" : ""}`}
            >
              {assetFound
                ? "Asset Found"
                : isCapturing
                  ? "Stop Auto"
                  : "Start Auto"}
            </button>
            <button onClick={captureFrame} className="capture-btn">
              Capture
            </button>
          </div>

          <div className="button-group">
            <button
              onClick={toggleStats}
              className={`stats-btn ${showStats ? "active" : ""}`}
            >
              Stats
            </button>
          </div>
        </div>
      </div>

      {error && <div className="ar-error">{error}</div>}

      <div className="ar-viewport">
        <video ref={videoRef} autoPlay muted playsInline className="ar-video" />

        <canvas
          ref={canvasRef}
          className="ar-canvas"
          style={{ display: "none" }}
        />

        <div ref={overlayRef} className="ar-overlay">
          {/* HTML Model Overlay - Use iframe for all models */}
          {showARCanvas && modelVisible && (
            <div
              className="ar-model-container webgl overlay"
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
                opacity: 1.0,
                pointerEvents: "auto",
                zIndex: 10,
              }}
            >
              <iframe
                src={`/models/${activeModel}.html`}
                className="ar-model-iframe"
                title={`3D ${activeModel} AR Overlay`}
                style={{
                  width: "100%",
                  height: "100%",
                  border: "none",
                  background: "transparent",
                  pointerEvents: "auto",
                }}
              />
            </div>
          )}

          {/* Fallback HTML model for when AR Canvas is disabled */}
          {!showARCanvas && modelVisible && (
            <div
              className="ar-model-container fallback overlay"
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
                opacity: 1.0,
                pointerEvents: "auto",
                zIndex: 10,
              }}
            >
              <iframe
                src={`/models/${activeModel}.html`}
                className="ar-model-iframe"
                title={`3D ${activeModel} Overlay`}
                style={{
                  width: "100%",
                  height: "100%",
                  border: "none",
                  background: "transparent",
                  pointerEvents: "auto",
                }}
              />
            </div>
          )}
        </div>
      </div>

      {/* <div className="ar-status">
        <div className="status-row">
          <span
            className={`status-indicator ${isStreaming ? "streaming" : "stopped"}`}
          >
            {isStreaming ? "Camera Active" : "Camera Inactive"}
          </span>
          <span
            className={`status-indicator ${isCapturing ? "capturing" : "idle"}`}
          >
            {isCapturing ? "Auto Capturing" : "Idle"}
          </span>
          <span className={`status-indicator ar-${arSession.trackingQuality}`}>
            AR: {arSession.trackingQuality.toUpperCase()}
          </span>
        </div>

        <div className="ar-session-info">
          <div className="session-stats">
            <span>Frames: {captureStats.framesProcessed}</span>
            <span>Matches: {captureStats.matchesFound}</span>
            <span>AR Init: {arSession.isInitialized ? "Yes" : "No"}</span>
            <span>Tracking: {arSession.isTracking ? "Active" : "Lost"}</span>
          </div>

          {debugInfo && (
            <div className="debug-info">
              <span>Debug: {debugInfo}</span>
            </div>
          )}
        </div>

        {lastMatch && (
          <div
            className={`match-result ${lastMatch.result.matched ? "found" : "not-found"}`}
          >
            {lastMatch.result.matched ? (
              <div>
                ✓ Asset Matched: {lastMatch.result.asset?.filename || "Unknown"}
                (Score: {(lastMatch.result.similarity_score * 100).toFixed(1)}%)
              </div>
            ) : (
              <div>✗ No Asset Match</div>
            )}
          </div>
        )}
      </div> */}
    </div>
  );
};
