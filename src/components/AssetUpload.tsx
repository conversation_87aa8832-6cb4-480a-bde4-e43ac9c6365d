import React, { useState, useRef, useEffect } from "react";
import { apiService, Campaign } from "../services/api";
import "./AssetUpload.css";

interface AssetUploadProps {
  onSuccess: () => void;
  onCancel: () => void;
}

const AssetUpload: React.FC<AssetUploadProps> = ({ onSuccess, onCancel }) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [error, setError] = useState<string>("");
  const [dragOver, setDragOver] = useState<boolean>(false);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [selectedCampaign, setSelectedCampaign] = useState<string>("");
  const [loadingCampaigns, setLoadingCampaigns] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const fetchCampaigns = async () => {
      try {
        setLoadingCampaigns(true);
        const data = await apiService.getCampaigns(1, 100); // Get first 100 campaigns
        setCampaigns(data.campaigns);
        if (data.campaigns.length > 0) {
          setSelectedCampaign(data.campaigns[0]._id);
        }
      } catch (err) {
        setError("Failed to load campaigns");
      } finally {
        setLoadingCampaigns(false);
      }
    };

    fetchCampaigns();
  }, []);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const validateFile = (file: File): boolean => {
    // Check file size (limit to 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      setError(`File "${file.name}" is too large. Maximum size is 10MB.`);
      return false;
    }

    // Check if it's an image file
    if (!file.type.startsWith('image/')) {
      setError(`File "${file.name}" is not an image. Only image files are allowed.`);
      return false;
    }

    return true;
  };

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const fileArray = Array.from(files);
    const validFiles = fileArray.filter(validateFile);
    
    if (validFiles.length > 0) {
      setSelectedFiles(prev => [...prev, ...validFiles]);
      setError("");
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0 || !selectedCampaign) return;

    setUploading(true);
    setError("");
    setUploadProgress(0);

    try {
      const total = selectedFiles.length;
      let completed = 0;

      for (const file of selectedFiles) {
        await apiService.uploadAsset(file, selectedCampaign);
        completed++;
        setUploadProgress((completed / total) * 100);
      }

      onSuccess();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Upload failed");
    } finally {
      setUploading(false);
    }
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="asset-upload-overlay">
      <div className="asset-upload-modal">
        <div className="asset-upload-header">
          <h2>Upload Assets</h2>
          <button className="close-button" onClick={onCancel} type="button">
            ×
          </button>
        </div>
        
        <div className="asset-upload-content">
          {error && <div className="error-message">{error}</div>}

          <div className="form-group" style={{marginBottom: "20px"}}>
            <label htmlFor="campaign-select" style={{display: "block", marginBottom: "5px", fontWeight: "bold"}}>
              Select Campaign *
            </label>
            {loadingCampaigns ? (
              <div style={{padding: "10px", color: "#666"}}>Loading campaigns...</div>
            ) : (
              <select
                id="campaign-select"
                value={selectedCampaign}
                onChange={(e) => setSelectedCampaign(e.target.value)}
                disabled={uploading || campaigns.length === 0}
                style={{
                  width: "100%",
                  padding: "8px",
                  border: "1px solid #ddd",
                  borderRadius: "4px",
                  fontSize: "14px"
                }}
              >
                {campaigns.length === 0 ? (
                  <option value="">No campaigns available</option>
                ) : (
                  campaigns.map((campaign) => (
                    <option key={campaign._id} value={campaign._id}>
                      {campaign.name}
                    </option>
                  ))
                )}
              </select>
            )}
          </div>

          <div 
            className={`file-drop-zone ${dragOver ? 'drag-over' : ''} ${selectedFiles.length > 0 ? 'has-files' : ''}`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={handleBrowseClick}
          >
            <div className="drop-zone-text">
              {selectedFiles.length > 0 
                ? `${selectedFiles.length} file(s) selected`
                : "Drag and drop images here or click to browse"
              }
            </div>
            <button type="button" className="browse-button">
              Browse Files
            </button>
            <input
              ref={fileInputRef}
              type="file"
              className="file-input"
              multiple
              accept="image/*"
              onChange={handleFileInputChange}
            />
          </div>

          {selectedFiles.length > 0 && (
            <div className="selected-files">
              <h3>Selected Files:</h3>
              <ul className="file-list">
                {selectedFiles.map((file, index) => (
                  <li key={index} className="file-item">
                    <div className="file-info">
                      <div className="file-name">{file.name}</div>
                      <div className="file-size">{formatFileSize(file.size)}</div>
                    </div>
                    <button
                      type="button"
                      className="remove-file-button"
                      onClick={() => removeFile(index)}
                      disabled={uploading}
                    >
                      Remove
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {uploading && (
            <div className="upload-progress">
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
              <div className="upload-status">
                Uploading... {Math.round(uploadProgress)}%
              </div>
            </div>
          )}

          <div className="upload-actions">
            <button 
              type="button" 
              onClick={onCancel} 
              className="cancel-button"
              disabled={uploading}
            >
              Cancel
            </button>
            <button 
              type="button" 
              onClick={handleUpload} 
              className="upload-button"
              disabled={selectedFiles.length === 0 || uploading || !selectedCampaign}
            >
              {uploading ? 'Uploading...' : `Upload ${selectedFiles.length} File(s)`}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssetUpload;