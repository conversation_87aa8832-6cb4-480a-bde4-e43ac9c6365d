.asset-list {
  padding: 20px;
}

.asset-header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.asset-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.asset-stats {
  color: #666;
  font-size: 14px;
}

.asset-model {
  min-width: 200px;
}

.asset-model-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.asset-model-dropdown {
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  background: white;
  cursor: pointer;
  flex: 1;
  min-width: 120px;
}

.asset-model-dropdown:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
  color: #999;
}

.asset-model-dropdown:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.update-model-button {
  background: #28a745;
  color: white;
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  white-space: nowrap;
  min-width: 60px;
}

.update-model-button:hover:not(:disabled) {
  background: #218838;
}

.update-model-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.asset-stream {
  min-width: 120px;
  text-align: center;
}

.stream-video-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  white-space: nowrap;
}

.stream-video-button:hover {
  background: #0056b3;
}

.stop-stream-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  white-space: nowrap;
}

.stop-stream-button:hover {
  background: #c82333;
}

.upload-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.upload-button:hover {
  background: #0056b3;
}

.asset-table-container {
  overflow-x: auto;
  margin-bottom: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.asset-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.asset-table th,
.asset-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.asset-table th {
  background: #f8f9fa;
  font-weight: bold;
  border-bottom: 2px solid #ddd;
}

.asset-table tr:hover {
  background: #f5f5f5;
}

.asset-id {
  font-family: monospace;
  font-size: 12px;
  max-width: 120px;
  word-break: break-all;
}

.asset-filename {
  font-weight: 500;
  max-width: 200px;
  word-break: break-word;
}

.asset-type {
  font-size: 12px;
  color: #666;
  font-family: monospace;
}

.asset-size {
  font-size: 12px;
  color: #666;
}

.asset-campaign {
  font-family: monospace;
  font-size: 12px;
  max-width: 120px;
  word-break: break-all;
}


.asset-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.copy-url-button {
  background: #28a745;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  white-space: nowrap;
}

.copy-url-button:hover {
  background: #1e7e34;
}

.delete-asset-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  min-width: 32px;
}

.delete-asset-button:hover {
  background: #c82333;
}

.asset-loading, .asset-error, .asset-empty {
  text-align: center;
  padding: 40px;
  color: #666;
}

.asset-error {
  color: #dc3545;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}

.pagination-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.pagination-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.pagination-info {
  font-size: 14px;
  color: #666;
}