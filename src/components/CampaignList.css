.campaign-list {
  max-width: 1200px;
  margin: 0 auto;
}

.campaign-header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.campaign-list h2 {
  color: #333;
  margin: 0;
  font-size: 2rem;
}

.campaign-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.campaign-stats {
  color: #666;
  font-size: 0.9rem;
}

.add-button {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}

.add-button:hover {
  background-color: #218838;
}

.campaign-table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  overflow-x: auto;
}

.campaign-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.campaign-table th,
.campaign-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.campaign-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
  position: sticky;
  top: 0;
}

.campaign-table tr:hover {
  background-color: #f8f9fa;
}

.campaign-table tr:last-child td {
  border-bottom: none;
}

.campaign-name {
  font-weight: 600;
  color: #333;
}

.status-select {
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
  cursor: pointer;
}

.status-select:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.ar-viewer-button,
.stream-button,
.edit-button,
.delete-button {
  background: none;
  border: none;
  padding: 0.25rem;
  cursor: pointer;
  font-size: 1rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.ar-viewer-button:hover {
  background-color: #fff3cd;
}

.stream-button:hover {
  background-color: #e8f5e8;
}

.edit-button:hover {
  background-color: #e3f2fd;
}

.delete-button:hover {
  background-color: #ffebee;
}

.campaign-loading,
.campaign-error,
.campaign-empty {
  text-align: center;
  padding: 3rem;
  color: #666;
  font-size: 1.1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.campaign-error {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  margin: 1rem 0;
}

.campaign-loading {
  color: #007bff;
}

.campaign-empty {
  color: #6c757d;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding: 1rem;
}

.pagination-button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}

.pagination-button:hover:not(:disabled) {
  background-color: #0056b3;
}

.pagination-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.pagination-info {
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
}

@media (max-width: 768px) {
  .campaign-header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .campaign-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .campaign-table {
    font-size: 0.8rem;
  }
  
  .campaign-table th,
  .campaign-table td {
    padding: 0.5rem;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }
}