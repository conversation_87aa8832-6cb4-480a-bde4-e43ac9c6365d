import React, { useState } from "react";
import "./LoginPage.css";

/**
 * Props interface for the LoginPage component.
 * 
 * @interface LoginPageProps
 */
interface LoginPageProps {
  /**
   * Callback function triggered when user successfully authenticates.
   * Receives the JWT access token from the authentication API.
   * 
   * @param token - The JWT access token returned from successful authentication
   */
  onLogin: (token: string) => void;
}

/**
 * A React component that provides user authentication functionality for the HackReality platform.
 * 
 * This component renders a login form with username and password fields, handles form submission,
 * performs authentication via the REST API, and manages loading and error states. Upon successful
 * authentication, it calls the onLogin callback with the received JWT token.
 * 
 * The component makes direct API calls to the authentication endpoint and handles various error
 * scenarios including network errors and authentication failures. It provides visual feedback
 * through loading states and error messages.
 * 
 * @component
 * @param props - The component props
 * @param props.onLogin - Callback function called with the JWT token upon successful authentication
 * 
 * @example
 * ```tsx
 * // Basic usage in App component
 * import LoginPage from './components/LoginPage';
 * 
 * function App() {
 *   const handleLogin = (token: string) => {
 *     localStorage.setItem('token', token);
 *     setIsAuthenticated(true);
 *   };
 * 
 *   return (
 *     <LoginPage onLogin={handleLogin} />
 *   );
 * }
 * ```
 * 
 * @example
 * ```tsx
 * // Usage with authentication hook
 * import LoginPage from './components/LoginPage';
 * import { useAuth } from './hooks/useAuth';
 * 
 * function AuthenticatedApp() {
 *   const { login } = useAuth();
 * 
 *   return (
 *     <LoginPage onLogin={login} />
 *   );
 * }
 * ```
 * 
 * @returns The rendered login form component
 */
const LoginPage: React.FC<LoginPageProps> = ({ onLogin }) => {
  /** Current username input value */
  const [username, setUsername] = useState<string>("");
  
  /** Current password input value */
  const [password, setPassword] = useState<string>("");
  
  /** Loading state indicating if authentication request is in progress */
  const [isLoading, setIsLoading] = useState<boolean>(false);
  
  /** Error message to display to user, empty string when no error */
  const [error, setError] = useState<string>("");

  /**
   * Handles form submission for user authentication.
   * 
   * Prevents default form submission, validates inputs, makes API request to authentication
   * endpoint, and handles success/error responses. On successful authentication, calls
   * the onLogin callback with the received JWT token. Updates loading and error states
   * throughout the process.
   * 
   * @param e - The form submission event
   * 
   * @example
   * ```tsx
   * // Form submission will trigger this handler
   * <form onSubmit={handleSubmit}>
   *   // form fields
   * </form>
   * ```
   * 
   * @returns Promise that resolves when authentication request completes
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/auth/login`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ username, password }),
        },
      );

      const data = await response.json();

      if (response.ok) {
        onLogin(data.access_token);
      } else {
        setError(data.message || "Login failed");
      }
    } catch (err) {
      setError("Network error. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="login-page">
      <div className="login-container">
        <h2>Login</h2>
        <form onSubmit={handleSubmit} className="login-form">
          <div className="form-group">
            <label htmlFor="username">Username:</label>
            <input
              id="username"
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="Enter your username"
              required
            />
          </div>
          <div className="form-group">
            <label htmlFor="password">Password:</label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              required
            />
          </div>
          {error && <div className="error-message">{error}</div>}
          <button type="submit" disabled={isLoading} className="login-button">
            {isLoading ? "Logging in..." : "Login"}
          </button>
        </form>
      </div>
    </div>
  );
};

export default LoginPage;
