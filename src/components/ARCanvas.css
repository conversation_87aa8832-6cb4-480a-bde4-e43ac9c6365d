.ar-canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: transparent;
}

.ar-canvas {
  display: block;
  width: 100%;
  height: 100%;
  background: transparent;
  cursor: crosshair;
}

.ar-canvas-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(220, 53, 69, 0.9);
  color: white;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-align: center;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 100;
}

.ar-canvas-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #007bff;
  animation: ar-spin 1s linear infinite;
}

@keyframes ar-spin {
  to {
    transform: rotate(360deg);
  }
}

.ar-canvas-stats {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  min-width: 200px;
  z-index: 50;
  backdrop-filter: blur(4px);
}

.stats-section {
  margin-bottom: 1rem;
}

.stats-section:last-child {
  margin-bottom: 0;
}

.stats-section h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: #00aaff;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 0.25rem 1rem;
  align-items: center;
}

.stats-grid span:first-child {
  color: #ccc;
}

.stats-grid span:nth-child(2n) {
  color: white;
  font-weight: 500;
  text-align: right;
}

.ar-canvas-debug {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  z-index: 50;
}

.debug-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.debug-info span {
  color: #aaa;
}

.debug-info span:first-child {
  color: #00aaff;
  font-weight: 500;
}

/* Performance optimizations */
.ar-canvas-container {
  will-change: contents;
}

.ar-canvas {
  will-change: auto;
}

.ar-canvas-stats,
.ar-canvas-debug {
  will-change: opacity;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ar-canvas-stats {
    top: 5px;
    left: 5px;
    padding: 0.75rem;
    min-width: 150px;
    font-size: 0.7rem;
  }
  
  .stats-section h4 {
    font-size: 0.8rem;
  }
  
  .ar-canvas-debug {
    bottom: 5px;
    left: 5px;
    padding: 0.5rem;
    font-size: 0.7rem;
  }
  
  .ar-canvas-loading {
    padding: 1.5rem;
  }
  
  .loading-spinner {
    width: 30px;
    height: 30px;
  }
}

@media (max-width: 480px) {
  .ar-canvas-stats {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    border-radius: 0 0 8px 8px;
    backdrop-filter: none;
    background: rgba(0, 0, 0, 0.9);
  }
  
  .stats-grid {
    grid-template-columns: 1fr auto;
    gap: 0.125rem 0.5rem;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .ar-canvas-stats,
  .ar-canvas-debug {
    background: rgba(0, 0, 0, 0.95);
    border: 1px solid white;
  }
  
  .ar-canvas-error {
    border: 2px solid white;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner {
    animation: none;
    border-top-color: transparent;
  }
  
  .ar-canvas-stats,
  .ar-canvas-debug {
    transition: none;
  }
}

/* Focus styles for accessibility */
.ar-canvas:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.ar-canvas-container:focus-within .ar-canvas-stats,
.ar-canvas-container:focus-within .ar-canvas-debug {
  border: 1px solid #007bff;
}