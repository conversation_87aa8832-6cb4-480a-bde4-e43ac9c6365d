.pose-overlay-container {
  position: relative;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.pose-overlay-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 20;
  pointer-events: none;
}

.pose-overlay-legend {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1rem;
  background: rgba(0, 0, 0, 0.7);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  z-index: 21;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.pose-overlay-status {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  z-index: 21;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(0, 0, 0, 0.7);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-indicator span {
  font-size: 0.6rem;
  animation: pulse 2s infinite;
}

.status-indicator.drawing span {
  color: #17a2b8;
}

.status-indicator.active span {
  color: #28a745;
}

.status-indicator.matched span {
  color: #ffc107;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pose-overlay-legend {
    bottom: 60px;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }
  
  .legend-item {
    font-size: 0.7rem;
  }
  
  .legend-color {
    width: 10px;
    height: 10px;
  }
  
  .pose-overlay-status {
    top: 5px;
    right: 5px;
  }
  
  .status-indicator {
    padding: 0.2rem 0.5rem;
    font-size: 0.7rem;
  }
}

/* Animation for smooth transitions */
.pose-overlay-canvas {
  transition: opacity 0.3s ease;
}

.pose-overlay-container.hidden .pose-overlay-canvas {
  opacity: 0;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .pose-overlay-legend {
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid white;
  }
  
  .status-indicator {
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
  
  .legend-color {
    border: 2px solid white;
  }
}

/* Performance optimization for animations */
.pose-overlay-canvas,
.status-indicator span {
  will-change: opacity;
}

/* Focus styles for accessibility */
.pose-overlay-container:focus-within .pose-overlay-legend,
.pose-overlay-container:focus-within .pose-overlay-status {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}