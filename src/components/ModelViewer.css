.model-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
  color: white;
}

.model-viewer-header {
  background-color: #282c34;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #404040;
  flex-shrink: 0;
}

.model-viewer-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: white;
}

.model-selector-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.model-selector-container label {
  color: #ccc;
  font-size: 0.9rem;
}

.model-select {
  padding: 0.5rem 1rem;
  border: 1px solid #404040;
  border-radius: 4px;
  background-color: #333;
  color: white;
  font-size: 0.9rem;
  min-width: 120px;
}

.model-select:focus {
  outline: none;
  border-color: #007acc;
}

.model-viewer-content {
  display: flex;
  flex: 1;
  gap: 1rem;
  padding: 1rem;
  min-height: 0;
}

.model-iframe-container {
  flex: 2;
  background-color: #222;
  border-radius: 8px;
  border: 1px solid #404040;
  overflow: hidden;
  min-height: 500px;
}

.model-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 8px;
}

.model-info {
  flex: 1;
  background-color: #252525;
  border-radius: 8px;
  border: 1px solid #404040;
  padding: 1.5rem;
  max-width: 300px;
}

.model-info h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #007acc;
  font-size: 1.2rem;
}

.model-details {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.model-details p {
  margin: 0;
  color: #ccc;
  line-height: 1.4;
}

.model-details strong {
  color: white;
}

@media (max-width: 768px) {
  .model-viewer-content {
    flex-direction: column;
  }
  
  .model-info {
    max-width: none;
  }
  
  .model-iframe-container {
    min-height: 400px;
  }
}