/**
 * @fileoverview Main dashboard component providing navigation and content management.
 * Features lazy-loaded components for optimal performance and tabbed navigation.
 * 
 * <AUTHOR> Platform
 * @version 1.0.0
 */

import React, { useState, useMemo, Suspense, lazy } from 'react';
import './Dashboard.css';

// Lazy load heavy components for code splitting and performance
const Campaigns = lazy(() => import('./Campaigns'));
const AssetList = lazy(() => import('./AssetList'));
const ModelViewer = lazy(() => import('./ModelViewer'));

/**
 * Props interface for the Dashboard component.
 * 
 * @public
 */
interface DashboardProps {
  /** Callback function to handle user logout */
  onLogout: () => void;
}

/**
 * Main dashboard component for authenticated users.
 * Provides tabbed navigation between different application sections:
 * Home, Campaigns, Assets, and 3D Model Viewer.
 * 
 * Uses lazy loading for performance optimization and Suspense for loading states.
 * 
 * @param props - Component configuration
 * @returns JSX element containing the dashboard interface
 * 
 * @public
 */
const Dashboard: React.FC<DashboardProps> = ({ onLogout }) => {
  const [activeView, setActiveView] = useState<string>('home');

  const renderContent = useMemo(() => {
    switch (activeView) {
      case 'campaigns':
        return <Campaigns />;
      case 'assets':
        return <AssetList />;
      case 'models':
        return <ModelViewer />;
      default:
        return (
          <div className="dashboard-home">
            <h2>Welcome to HackReality Client</h2>
            <p>Select an option from the navigation menu to get started.</p>
          </div>
        );
    }
  }, [activeView]);

  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <h1>HackReality Client</h1>
        <p>API URL: {process.env.REACT_APP_API_URL}</p>
        <button onClick={onLogout} className="logout-button">
          Logout
        </button>
      </header>
      
      <nav className="dashboard-nav">
        <button 
          className={activeView === 'home' ? 'nav-button active' : 'nav-button'}
          onClick={() => setActiveView('home')}
        >
          Home
        </button>
        <button 
          className={activeView === 'campaigns' ? 'nav-button active' : 'nav-button'}
          onClick={() => setActiveView('campaigns')}
        >
          Campaigns
        </button>
        <button 
          className={activeView === 'assets' ? 'nav-button active' : 'nav-button'}
          onClick={() => setActiveView('assets')}
        >
          Assets
        </button>
        <button 
          className={activeView === 'models' ? 'nav-button active' : 'nav-button'}
          onClick={() => setActiveView('models')}
        >
          3D Models
        </button>
      </nav>

      <main className="dashboard-content">
        <Suspense fallback={<div className="loading-spinner">Loading...</div>}>
          {renderContent}
        </Suspense>
      </main>
    </div>
  );
};

export default Dashboard;