import React, { useState, useEffect, useCallback } from "react";
import {
  Campaign,
  CampaignListResponse,
  CreateCampaignRequest,
  UpdateCampaignRequest,
  // UpdateStatusRequest,
  apiService,
} from "../services/api";
import CampaignForm from "./CampaignForm";
import "./CampaignList.css";

interface CampaignListProps {
  onOpenARViewer?: (campaign: Campaign) => void;
}

const CampaignList: React.FC<CampaignListProps> = ({ onOpenARViewer }) => {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>("");
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    size: 10,
  });
  const [showForm, setShowForm] = useState<boolean>(false);
  const [editingCampaign, setEditingCampaign] = useState<Campaign | null>(null);
  const [formLoading, setFormLoading] = useState<boolean>(false);

  const fetchCampaigns = useCallback(async () => {
    try {
      setLoading(true);
      setError("");
      const data: CampaignListResponse = await apiService.getCampaigns(
        pagination.page,
        pagination.size,
      );
      setCampaigns(data.campaigns);
      setPagination({
        total: data.total,
        page: data.page,
        size: data.size,
      });
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to fetch campaigns",
      );
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.size]);

  useEffect(() => {
    fetchCampaigns();
  }, [fetchCampaigns]);

  const getStatusColor = (status: Campaign["status"]) => {
    switch (status) {
      case "Draft":
        return "#6c757d";
      case "Paused":
        return "#ffc107";
      case "Running":
        return "#28a745";
      case "Stopped":
        return "#dc3545";
      case "Terminated":
        return "#343a40";
      default:
        return "#6c757d";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (loading) {
    return <div className="campaign-loading">Loading campaigns...</div>;
  }

  if (error) {
    return <div className="campaign-error">Error: {error}</div>;
  }

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  const handleAddCampaign = () => {
    setEditingCampaign(null);
    setShowForm(true);
  };

  const handleEditCampaign = (campaign: Campaign) => {
    setEditingCampaign(campaign);
    setShowForm(true);
  };

  const handleFormSubmit = async (
    data: CreateCampaignRequest | UpdateCampaignRequest,
  ) => {
    try {
      setFormLoading(true);
      if (editingCampaign) {
        await apiService.updateCampaign(
          editingCampaign._id,
          data as UpdateCampaignRequest,
        );
      } else {
        await apiService.createCampaign(data as CreateCampaignRequest);
      }
      setShowForm(false);
      setEditingCampaign(null);
      await fetchCampaigns();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to save campaign");
    } finally {
      setFormLoading(false);
    }
  };

  const handleFormCancel = () => {
    setShowForm(false);
    setEditingCampaign(null);
  };

  const handleStatusChange = async (
    campaign: Campaign,
    newStatus: "Draft" | "Paused" | "Running" | "Stopped" | "Terminated",
  ) => {
    try {
      await apiService.updateCampaignStatus(campaign._id, {
        status: newStatus,
      });
      await fetchCampaigns();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to update status");
    }
  };

  const handleDeleteCampaign = async (campaign: Campaign) => {
    if (window.confirm(`Are you sure you want to delete "${campaign.name}"?`)) {
      try {
        await apiService.deleteCampaign(campaign._id);
        await fetchCampaigns();
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to delete campaign",
        );
      }
    }
  };

  const totalPages = Math.ceil(pagination.total / pagination.size);

  return (
    <div className="campaign-list">
      <div className="campaign-header-section">
        <h2>Campaigns</h2>
        <div className="campaign-actions">
          <span className="campaign-stats">
            Total: {pagination.total} campaigns
          </span>
          <button onClick={handleAddCampaign} className="add-button">
            Add Campaign
          </button>
        </div>
      </div>

      {campaigns.length === 0 ? (
        <div className="campaign-empty">No campaigns found.</div>
      ) : (
        <div className="campaign-table-container">
          <table className="campaign-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Creator</th>
                <th>Status</th>
                <th>Created</th>
                <th>Updated</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {campaigns.map((campaign) => (
                <tr key={campaign._id}>
                  <td
                    className="campaign-id"
                    onClick={() => navigator.clipboard.writeText(campaign._id)}
                    title="Click to copy ID"
                    style={{ cursor: "pointer", fontFamily: "monospace" }}
                  >
                    {campaign._id}
                  </td>
                  <td className="campaign-name">{campaign.name}</td>
                  <td>{campaign.creator_id}</td>
                  <td>
                    <select
                      value={campaign.status}
                      onChange={(e) =>
                        handleStatusChange(campaign, e.target.value as any)
                      }
                      className="status-select"
                      style={{
                        backgroundColor: getStatusColor(campaign.status),
                      }}
                    >
                      <option value="Draft">Draft</option>
                      <option value="Paused">Paused</option>
                      <option value="Running">Running</option>
                      <option value="Stopped">Stopped</option>
                      <option value="Terminated">Terminated</option>
                    </select>
                  </td>
                  <td>{formatDate(campaign.created_at)}</td>
                  <td>{formatDate(campaign.updated_at)}</td>
                  <td>
                    <div className="action-buttons">
                      <button
                        onClick={() =>
                          window.open(
                            `/ar-viewer/${campaign._id}`,
                            "_blank",
                            "noopener,noreferrer",
                          )
                        }
                        className="ar-viewer-button"
                        title="Open AR Viewer in new tab"
                      >
                        📱
                      </button>
                      <button
                        onClick={() => handleEditCampaign(campaign)}
                        className="edit-button"
                        title="Edit"
                      >
                        ✏️
                      </button>
                      <button
                        onClick={() => handleDeleteCampaign(campaign)}
                        className="delete-button"
                        title="Delete"
                      >
                        🗑️
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {campaigns.length > 0 && totalPages > 1 && (
        <div className="pagination">
          <button
            onClick={() => handlePageChange(pagination.page - 1)}
            disabled={pagination.page === 1}
            className="pagination-button"
          >
            Previous
          </button>

          <span className="pagination-info">
            Page {pagination.page} of {totalPages}
          </span>

          <button
            onClick={() => handlePageChange(pagination.page + 1)}
            disabled={pagination.page === totalPages}
            className="pagination-button"
          >
            Next
          </button>
        </div>
      )}

      {showForm && (
        <CampaignForm
          campaign={editingCampaign || undefined}
          onSubmit={handleFormSubmit}
          onCancel={handleFormCancel}
          isLoading={formLoading}
        />
      )}
    </div>
  );
};

export default CampaignList;
