import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { PoseAnalysisResult, KeyPoint, Pose3D } from '../types/pose';
import { MatchResult } from '../services/frameCapture';
import './PoseOverlay.css';

interface PoseOverlayProps {
  width: number;
  height: number;
  poseData?: PoseAnalysisResult;
  matchResult?: MatchResult;
  showKeypoints?: boolean;
  showMatches?: boolean;
  showPoseInfo?: boolean;
  onPoseUpdate?: (pose: Pose3D | null) => void;
}

export const PoseOverlay: React.FC<PoseOverlayProps> = ({
  width,
  height,
  poseData,
  matchResult,
  showKeypoints = true,
  showMatches = true,
  showPoseInfo = true,
  onPoseUpdate
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [lastFrameTime, setLastFrameTime] = useState(0);

  // Colors for different elements - memoized to prevent re-renders
  const colors = useMemo(() => ({
    keypoint: '#00ff00',
    keypointMatched: '#ffff00',
    match: '#ff0000',
    pose: '#00ffff',
    confidence: '#ffffff',
    background: 'rgba(0, 0, 0, 0.3)'
  }), []);

  // Draw keypoints on canvas
  const drawKeypoints = useCallback((ctx: CanvasRenderingContext2D, keypoints: KeyPoint[], color: string = colors.keypoint) => {
    ctx.save();
    ctx.fillStyle = color;
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;

    keypoints.forEach(kp => {
      // Draw keypoint circle
      ctx.beginPath();
      ctx.arc(kp.x, kp.y, Math.max(2, kp.size / 4), 0, 2 * Math.PI);
      ctx.fill();
      
      // Draw orientation line if angle is available
      if (kp.angle >= 0) {
        const length = Math.max(10, kp.size / 2);
        const endX = kp.x + Math.cos(kp.angle * Math.PI / 180) * length;
        const endY = kp.y + Math.sin(kp.angle * Math.PI / 180) * length;
        
        ctx.beginPath();
        ctx.moveTo(kp.x, kp.y);
        ctx.lineTo(endX, endY);
        ctx.stroke();
      }
    });

    ctx.restore();
  }, [colors]);

  // Draw pose information
  const drawPoseInfo = useCallback((ctx: CanvasRenderingContext2D, pose: Pose3D, x: number, y: number) => {
    ctx.save();
    ctx.fillStyle = colors.background;
    ctx.fillRect(x - 5, y - 5, 200, 120);
    
    ctx.fillStyle = colors.pose;
    ctx.font = '12px monospace';
    ctx.textAlign = 'left';
    
    const lines = [
      `Translation:`,
      `  X: ${pose.translation.x.toFixed(3)}`,
      `  Y: ${pose.translation.y.toFixed(3)}`,
      `  Z: ${pose.translation.z.toFixed(3)}`,
      `Rotation:`,
      `  X: ${pose.rotation.x.toFixed(3)}`,
      `  Y: ${pose.rotation.y.toFixed(3)}`,
      `  Z: ${pose.rotation.z.toFixed(3)}`,
      `Confidence: ${(pose.confidence * 100).toFixed(1)}%`
    ];
    
    lines.forEach((line, index) => {
      ctx.fillText(line, x, y + index * 14);
    });
    
    ctx.restore();
  }, [colors]);

  // Draw match result information
  const drawMatchInfo = useCallback((ctx: CanvasRenderingContext2D, match: MatchResult) => {
    const x = 10;
    const y = height - 100;
    
    ctx.save();
    ctx.fillStyle = colors.background;
    ctx.fillRect(x - 5, y - 5, 250, 90);
    
    const isMatched = match.result.matched;
    ctx.fillStyle = isMatched ? colors.keypointMatched : colors.match;
    ctx.font = 'bold 14px Arial';
    ctx.textAlign = 'left';
    
    ctx.fillText(
      isMatched ? '✓ MATCH FOUND' : '✗ NO MATCH', 
      x, y
    );
    
    ctx.fillStyle = colors.confidence;
    ctx.font = '12px monospace';
    
    const infoLines = [
      `Asset: ${match.result.asset?.filename || 'None'}`,
      `Score: ${(match.result.similarity_score * 100).toFixed(1)}%`,
      `Threshold: ${(match.result.threshold_used * 100).toFixed(1)}%`,
      `Processing: ${match.captureInfo.processingTime.toFixed(1)}ms`,
      `Frame: #${match.captureInfo.frameNumber}`
    ];
    
    infoLines.forEach((line, index) => {
      ctx.fillText(line, x, y + 20 + index * 12);
    });
    
    ctx.restore();
  }, [colors, height]);

  // Draw quality indicators
  const drawQualityIndicators = useCallback((ctx: CanvasRenderingContext2D, poseData: PoseAnalysisResult) => {
    const x = width - 150;
    const y = 20;
    const metrics = poseData.quality_metrics;
    
    ctx.save();
    ctx.fillStyle = colors.background;
    ctx.fillRect(x - 5, y - 5, 140, 100);
    
    ctx.fillStyle = colors.confidence;
    ctx.font = 'bold 12px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('Quality Metrics', x, y);
    
    ctx.font = '11px monospace';
    
    const qualityLines = [
      `Keypoints: ${(metrics.keypoint_visibility * 100).toFixed(0)}%`,
      `Motion: ${(100 - metrics.motion_blur * 100).toFixed(0)}%`,
      `Light: ${(metrics.illumination_quality * 100).toFixed(0)}%`,
      `Occlusion: ${(100 - metrics.occlusion_level * 100).toFixed(0)}%`,
      `Overall: ${(metrics.overall_quality * 100).toFixed(0)}%`,
      `Features: ${poseData.keypoints.feature_count}`
    ];
    
    qualityLines.forEach((line, index) => {
      const value = parseFloat(line.split(': ')[1]);
      if (!isNaN(value)) {
        ctx.fillStyle = value > 70 ? colors.keypoint : value > 40 ? colors.keypointMatched : colors.match;
      } else {
        ctx.fillStyle = colors.confidence;
      }
      ctx.fillText(line, x, y + 20 + index * 12);
    });
    
    ctx.restore();
  }, [colors, width]);

  // Draw performance stats
  const drawPerformanceStats = useCallback((ctx: CanvasRenderingContext2D, poseData: PoseAnalysisResult) => {
    const x = 10;
    const y = 20;
    const stats = poseData.processing_stats;
    
    ctx.save();
    ctx.fillStyle = colors.background;
    ctx.fillRect(x - 5, y - 5, 180, 80);
    
    ctx.fillStyle = colors.confidence;
    ctx.font = 'bold 12px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('Performance', x, y);
    
    ctx.font = '11px monospace';
    
    const perfLines = [
      `Detection: ${stats.detection_time_ms.toFixed(1)}ms`,
      `Matching: ${stats.matching_time_ms.toFixed(1)}ms`,
      `Pose Est: ${stats.pose_estimation_time_ms.toFixed(1)}ms`,
      `Total: ${stats.total_time_ms.toFixed(1)}ms`,
      `FPS: ${stats.total_time_ms > 0 ? (1000 / stats.total_time_ms).toFixed(1) : 'N/A'}`
    ];
    
    perfLines.forEach((line, index) => {
      ctx.fillText(line, x, y + 20 + index * 12);
    });
    
    ctx.restore();
  }, [colors]);

  // Main drawing function
  const drawOverlay = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    setIsDrawing(true);
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Draw keypoints if available and enabled
    if (showKeypoints && poseData?.keypoints) {
      drawKeypoints(ctx, poseData.keypoints.keypoints, colors.keypoint);
    }
    
    // Draw match information if available
    if (showMatches && matchResult) {
      drawMatchInfo(ctx, matchResult);
    }
    
    // Draw pose information if available and enabled
    if (showPoseInfo && poseData) {
      if (poseData.pose) {
        drawPoseInfo(ctx, poseData.pose, width - 220, height - 140);
      }
      
      // Draw quality indicators
      drawQualityIndicators(ctx, poseData);
      
      // Draw performance stats
      drawPerformanceStats(ctx, poseData);
    }
    
    // Draw error information if present
    if (poseData?.error) {
      ctx.save();
      ctx.fillStyle = 'rgba(220, 53, 69, 0.8)';
      ctx.fillRect(10, height / 2 - 30, width - 20, 60);
      
      ctx.fillStyle = 'white';
      ctx.font = '14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(
        `Error: ${poseData.error.error_message}`,
        width / 2,
        height / 2
      );
      ctx.restore();
    }
    
    setIsDrawing(false);
    setLastFrameTime(performance.now());
  }, [
    width, height, poseData, matchResult, showKeypoints, showMatches, showPoseInfo,
    drawKeypoints, drawMatchInfo, drawPoseInfo, drawQualityIndicators, drawPerformanceStats, colors
  ]);

  // Effect to redraw when data changes
  useEffect(() => {
    const currentTime = performance.now();
    // Throttle drawing to max 30 FPS for performance
    if (currentTime - lastFrameTime > 33) {
      requestAnimationFrame(drawOverlay);
    }
  }, [poseData, matchResult, drawOverlay, lastFrameTime]);

  // Effect to notify parent of pose updates
  useEffect(() => {
    if (onPoseUpdate && poseData?.pose) {
      onPoseUpdate(poseData.pose);
    }
  }, [poseData?.pose, onPoseUpdate]);

  // Handle canvas resize
  useEffect(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      canvas.width = width;
      canvas.height = height;
      drawOverlay();
    }
  }, [width, height, drawOverlay]);

  return (
    <div className="pose-overlay-container">
      <canvas
        ref={canvasRef}
        className="pose-overlay-canvas"
        width={width}
        height={height}
        style={{ width, height }}
      />
      
      {/* Legend */}
      <div className="pose-overlay-legend">
        {showKeypoints && (
          <div className="legend-item">
            <div className="legend-color" style={{ backgroundColor: colors.keypoint }}></div>
            <span>Keypoints</span>
          </div>
        )}
        {showMatches && matchResult?.result.matched && (
          <div className="legend-item">
            <div className="legend-color" style={{ backgroundColor: colors.keypointMatched }}></div>
            <span>Match Found</span>
          </div>
        )}
        {showPoseInfo && poseData?.pose && (
          <div className="legend-item">
            <div className="legend-color" style={{ backgroundColor: colors.pose }}></div>
            <span>Pose Data</span>
          </div>
        )}
      </div>
      
      {/* Status indicators */}
      <div className="pose-overlay-status">
        {isDrawing && (
          <div className="status-indicator drawing">
            <span>●</span> Rendering
          </div>
        )}
        {poseData && (
          <div className="status-indicator active">
            <span>●</span> Tracking
          </div>
        )}
        {matchResult?.result.matched && (
          <div className="status-indicator matched">
            <span>●</span> Matched
          </div>
        )}
      </div>
    </div>
  );
};

export default PoseOverlay;