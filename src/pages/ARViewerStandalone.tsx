import React from "react";
import { useParams } from "react-router-dom";
import { ARViewer } from "../components/ARViewer";
import "../components/ARViewer.css";

/**
 * Standalone AR Viewer page.
 * Reads campaignId from the URL and renders the ARViewer component.
 */
const ARViewerStandalone: React.FC = () => {
  // Get campaignId from the route params
  const { id } = useParams<{ id: string }>();

  if (!id) {
    return (
      <div style={{ padding: "2rem", color: "red" }}>
        <h2>Error: No campaign ID provided</h2>
        <p>Please specify a campaign ID in the URL.</p>
      </div>
    );
  }

  return (
    <div style={{ height: "100vh", background: "red" }}>
      <ARViewer campaignId={id} />
    </div>
  );
};

export default ARViewerStandalone;
