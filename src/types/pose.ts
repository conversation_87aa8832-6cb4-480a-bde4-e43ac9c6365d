/**
 * @fileoverview Comprehensive type definitions for pose estimation, tracking, and AR functionality.
 * This module defines all interfaces and types used throughout the pose estimation and augmented reality system.
 * 
 * <AUTHOR> AR System
 * @version 1.0.0
 */

/**
 * Represents a single keypoint detected in an image using computer vision algorithms.
 * Keypoints are distinctive features that can be reliably detected across different images.
 * 
 * @public
 */
export interface KeyPoint {
  /** X-coordinate of the keypoint in image space */
  x: number;
  /** Y-coordinate of the keypoint in image space */
  y: number;
  /** Orientation angle of the keypoint in radians */
  angle: number;
  /** Size/scale of the keypoint detector response */
  size: number;
  /** Strength of the keypoint response (higher = more distinctive) */
  response: number;
  /** Octave level where the keypoint was detected (for scale-space analysis) */
  octave: number;
  /** Classification ID for the keypoint type */
  class_id: number;
}

/**
 * Feature descriptor containing numerical representation of a keypoint's local appearance.
 * Used for matching keypoints between different images.
 * 
 * @public
 */
export interface Descriptor {
  /** Numerical descriptor data array */
  data: number[];
  /** Length of the descriptor vector */
  length: number;
}

/**
 * Collection of keypoints and descriptors detected in an image for pose estimation.
 * Combines spatial information with feature descriptors for robust matching.
 * 
 * @public
 */
export interface PoseKeypoints {
  /** Array of detected keypoints */
  keypoints: KeyPoint[];
  /** Corresponding feature descriptors for each keypoint */
  descriptors: Descriptor[];
  /** Width of the source image in pixels */
  image_width: number;
  /** Height of the source image in pixels */
  image_height: number;
  /** Confidence level of the detection algorithm (0-1) */
  detection_confidence: number;
  /** Total number of features detected */
  feature_count: number;
}

/**
 * Represents a complete 3D pose including position, orientation, and scale.
 * This is the primary output of the pose estimation system.
 * 
 * @public
 */
export interface Pose3D {
  /** 3D translation/position in world coordinates */
  translation: Vector3D;
  /** 3D orientation as a quaternion */
  rotation: Quaternion;
  /** 3D scale factors */
  scale: Vector3D;
  /** Confidence score of the pose estimation (0-1) */
  confidence: number;
  /** Optional tracking identifier for temporal consistency */
  tracking_id?: string;
}

/**
 * 3D vector representing position, direction, or scale.
 * 
 * @public
 */
export interface Vector3D {
  /** X-component */
  x: number;
  /** Y-component */
  y: number;
  /** Z-component */
  z: number;
}

/**
 * Quaternion representation of 3D rotation.
 * Provides smooth interpolation and avoids gimbal lock issues.
 * 
 * @public
 */
export interface Quaternion {
  /** X-component of the quaternion */
  x: number;
  /** Y-component of the quaternion */
  y: number;
  /** Z-component of the quaternion */
  z: number;
  /** W-component (scalar part) of the quaternion */
  w: number;
}

/**
 * Euler angles representation of 3D rotation using pitch, yaw, and roll.
 * Alternative to quaternion representation, more intuitive but prone to gimbal lock.
 * 
 * @public
 */
export interface EulerAngles {
  /** Pitch - rotation around x-axis (up/down) in radians */
  pitch: number;
  /** Yaw - rotation around y-axis (left/right) in radians */
  yaw: number;
  /** Roll - rotation around z-axis (tilt) in radians */
  roll: number;
}

// Pose similarity and matching types
export interface PoseSimilarityScore {
  overall_score: number;
  keypoint_similarity: number;
  descriptor_similarity: number;
  geometric_similarity: number;
  confidence: number;
  matched_keypoints: number;
  total_keypoints: number;
}

export interface PoseMatchResult {
  matched: boolean;
  similarity_score: PoseSimilarityScore;
  threshold_used: number;
  processing_time_ms: number;
  match_quality: 'excellent' | 'good' | 'fair' | 'poor';
}

// API response types for match-frame endpoint
export interface MatchFrameRequest {
  frame_image: Blob;
  threshold?: number;
  max_matches?: number;
  include_pose_data?: boolean;
}

export interface AssetPoseData {
  _id: string;
  campaign_id: string;
  organization_id: string | null;
  uploaded_by: string;
  filename: string;
  file_size: number;
  mime_type: string;
  url: string;
  thumbnail_url?: string;
  model: string | null;
  search_embedding_generated: boolean;
  pose_features_generated: boolean;
  pose_keypoints: PoseKeypoints | null;
  pose_descriptors: Descriptor[] | null;
  image_width: number | null;
  image_height: number | null;
  created_at: string;
  updated_at: string;
}

export interface MatchFrameApiResponse {
  result: {
    matched: boolean;
    found: boolean;
    asset: AssetPoseData | null;
    similarity_score: number;
    pose_match?: PoseMatchResult;
    threshold_used: number;
  };
  query_time_ms: number;
  frame_analysis: {
    keypoints_detected: number;
    pose_estimated: boolean;
    processing_quality: 'high' | 'medium' | 'low';
  };
}

// Pose tracking and temporal consistency
export interface PoseTracker {
  tracking_id: string;
  poses: TimestampedPose[];
  confidence_history: number[];
  last_update: number;
  stability_score: number;
}

export interface TimestampedPose {
  pose: Pose3D;
  timestamp: number;
  frame_number: number;
  quality_metrics: PoseQualityMetrics;
}

export interface PoseQualityMetrics {
  keypoint_visibility: number;
  motion_blur: number;
  illumination_quality: number;
  occlusion_level: number;
  overall_quality: number;
}

// Feature matching and correspondence
export interface KeypointMatch {
  query_keypoint: KeyPoint;
  reference_keypoint: KeyPoint;
  distance: number;
  confidence: number;
  match_type: 'exact' | 'approximate' | 'interpolated';
}

export interface FeatureCorrespondence {
  matches: KeypointMatch[];
  inlier_count: number;
  outlier_count: number;
  homography_matrix?: number[][]; // 3x3 matrix
  fundamental_matrix?: number[][]; // 3x3 matrix
}

// Pose estimation configuration
export interface PoseEstimationConfig {
  detection_threshold: number;
  max_keypoints: number;
  feature_type: 'ORB' | 'SIFT' | 'SURF' | 'AKAZE';
  matching_method: 'brute_force' | 'flann' | 'ratio_test';
  pose_solver: 'pnp' | 'pnp_ransac' | 'epnp' | 'dls';
  ransac_threshold: number;
  max_iterations: number;
}

// Camera and calibration types
export interface CameraIntrinsics {
  focal_length: Vector2D;
  principal_point: Vector2D;
  distortion_coefficients: number[];
  image_size: Vector2D;
}

export interface Vector2D {
  x: number;
  y: number;
}

export interface CameraExtrinsics {
  rotation: Quaternion;
  translation: Vector3D;
  view_matrix: number[][]; // 4x4 matrix
  projection_matrix: number[][]; // 4x4 matrix
}

// Pose validation and filtering
export interface PoseValidator {
  min_keypoints: number;
  max_reprojection_error: number;
  min_confidence: number;
  temporal_consistency_threshold: number;
  geometric_constraints: GeometricConstraints;
}

export interface GeometricConstraints {
  max_translation_change: number;
  max_rotation_change: number;
  max_scale_change: number;
  planarity_threshold: number;
}

// Pose transformation and utilities
export interface PoseTransformation {
  from_pose: Pose3D;
  to_pose: Pose3D;
  transformation_matrix: number[][]; // 4x4 matrix
  transformation_type: 'rigid' | 'similarity' | 'affine' | 'projective';
}

export interface PoseInterpolation {
  start_pose: Pose3D;
  end_pose: Pose3D;
  interpolation_factor: number; // 0.0 to 1.0
  interpolation_method: 'linear' | 'slerp' | 'cubic' | 'spline';
}

// Error and debugging types
export interface PoseEstimationError {
  error_type: 'insufficient_keypoints' | 'poor_matching' | 'geometric_inconsistency' | 'tracking_lost';
  error_message: string;
  suggested_actions: string[];
  error_code: number;
}

export interface PoseDebugInfo {
  keypoints_raw: KeyPoint[];
  keypoints_filtered: KeyPoint[];
  matches_raw: KeypointMatch[];
  matches_filtered: KeypointMatch[];
  pose_candidates: Pose3D[];
  selected_pose: Pose3D;
  debug_images?: {
    keypoints_overlay: string; // base64 image
    matches_overlay: string;   // base64 image
    pose_overlay: string;      // base64 image
  };
}

// Utility type for pose analysis results
export interface PoseAnalysisResult {
  pose: Pose3D | null;
  keypoints: PoseKeypoints;
  match_result: PoseMatchResult | null;
  quality_metrics: PoseQualityMetrics;
  processing_stats: {
    detection_time_ms: number;
    matching_time_ms: number;
    pose_estimation_time_ms: number;
    total_time_ms: number;
  };
  debug_info?: PoseDebugInfo;
  error?: PoseEstimationError;
}