/**
 * @fileoverview API service for HackReality platform providing campaign and asset management.
 * Handles REST API communication with authentication, caching, and error handling.
 * 
 * <AUTHOR> Platform
 * @version 1.0.0
 */

/**
 * Represents a campaign in the HackReality platform.
 * Campaigns organize assets and manage AR experiences.
 * 
 * @public
 */
export interface Campaign {
  /** Unique identifier for the campaign */
  _id: string;
  /** Human-readable name of the campaign */
  name: string;
  /** ID of the user who created the campaign */
  creator_id: string;
  /** Current status of the campaign lifecycle */
  status: "Draft" | "Paused" | "Running" | "Stopped" | "Terminated";
  /** ISO timestamp when the campaign was created */
  created_at: string;
  /** ISO timestamp when the campaign was last updated */
  updated_at: string;
}

export interface CampaignListResponse {
  campaigns: Campaign[];
  total: number;
  page: number;
  size: number;
}

export interface CreateCampaignRequest {
  name: string;
  status?: "Draft" | "Paused" | "Running" | "Stopped" | "Terminated";
}

export interface UpdateCampaignRequest {
  name?: string;
  creator_id?: string;
  status?: "Draft" | "Paused" | "Running" | "Stopped" | "Terminated";
}

export interface UpdateStatusRequest {
  status: "Draft" | "Paused" | "Running" | "Stopped" | "Terminated";
}

export interface Asset {
  _id: string;
  campaign_id: string;
  organization_id?: string;
  uploaded_by: string;
  filename: string;
  file_size: number;
  mime_type: string;
  url: string;
  thumbnail_url?: string;
  created_at: string;
  updated_at: string;
}

export interface AssetListResponse {
  assets: Asset[];
  total: number;
  page: number;
  size: number;
}

export interface MatchFrameResponse {
  result: {
    matched: boolean;
    asset: {
      _id: string;
      campaign_id: string;
      organization_id: string | null;
      uploaded_by: string;
      filename: string;
      file_size: number;
      mime_type: string;
      url: string;
      thumbnail_url?: string;
      model: string | null;
      search_embedding_generated: boolean;
      pose_features_generated: boolean;
      pose_keypoints: Array<{
        x: number;
        y: number;
        confidence: number;
      }> | null;
      pose_descriptors: Array<number[]> | null;
      image_width: number | null;
      image_height: number | null;
      created_at: string;
      updated_at: string;
    } | null;
    similarity_score: number;
    threshold_used: number;
    found: boolean;
  };
  query_time_ms: number;
}

/**
 * Main API service class providing centralized REST API communication.
 * Handles authentication, request/response processing, caching, and error handling.
 * 
 * @public
 */
export class ApiService {
  /** Base URL for API requests */
  private baseUrl: string;
  /** In-memory cache for API responses */
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  /** Cache timeout duration in milliseconds */
  private cacheTimeout: number = 5 * 60 * 1000; // 5 minutes

  /**
   * Creates a new ApiService instance with environment-appropriate base URL.
   */
  constructor() {
    // In development, use proxy. In production, use environment variable
    this.baseUrl =
      process.env.NODE_ENV === "production"
        ? process.env.REACT_APP_API_URL || ""
        : "/api/v1";
  }

  /**
   * Retrieves the authentication token from localStorage.
   * 
   * @returns The JWT token or null if not authenticated
   * @private
   */
  private getToken(): string | null {
    return localStorage.getItem("authToken");
  }

  /**
   * Retrieves cached data if still valid.
   * 
   * @param key - Cache key
   * @returns Cached data or null if expired/not found
   * @private
   */
  private getCachedData<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  /**
   * Stores data in cache with current timestamp.
   * 
   * @param key - Cache key
   * @param data - Data to cache
   * @private
   */
  private setCachedData<T>(key: string, data: T): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  /**
   * Makes an authenticated HTTP request to the API.
   * 
   * @param endpoint - API endpoint path
   * @param options - Fetch request options
   * @returns Promise resolving to typed response data
   * @throws ApiError when request fails
   * @private
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const token = this.getToken();
    const headers = {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      let errorMessage = `API Error: ${response.status} ${response.statusText}`;
      try {
        const text = await response.text();
        if (text) {
          errorMessage += ` - ${text}`;
        }
      } catch (e) {
        // Ignore errors when reading error response
      }
      throw new Error(errorMessage);
    }

    const text = await response.text();
    if (!text) {
      throw new Error('Empty response from server');
    }
    
    try {
      return JSON.parse(text);
    } catch (e) {
      throw new Error('Invalid JSON response from server');
    }
  }

  /**
   * Retrieves a paginated list of campaigns from the API.
   * Supports pagination with configurable page size and number.
   * 
   * @param page - Page number to retrieve (1-based indexing)
   * @param size - Number of campaigns per page
   * @returns Promise resolving to campaign list with pagination metadata
   * @throws Error when API request fails or returns invalid data
   * 
   * @example
   * ```typescript
   * const campaigns = await apiService.getCampaigns(1, 20);
   * console.log(`Total campaigns: ${campaigns.total}`);
   * ```
   */
  async getCampaigns(
    page: number = 1,
    size: number = 10,
  ): Promise<CampaignListResponse> {
    return this.request<CampaignListResponse>(
      `/campaigns/?page=${page}&size=${size}`,
    );
  }

  /**
   * Retrieves a specific campaign by its unique identifier.
   * 
   * @param id - Unique campaign identifier
   * @returns Promise resolving to the campaign object
   * @throws Error when campaign not found or API request fails
   * 
   * @example
   * ```typescript
   * const campaign = await apiService.getCampaign('campaign-123');
   * console.log(campaign.name); // Campaign name
   * ```
   */
  async getCampaign(id: string): Promise<Campaign> {
    return this.request<Campaign>(`/campaigns/${id}`);
  }

  /**
   * Creates a new campaign with the provided configuration.
   * Sends POST request with campaign data to the campaigns endpoint.
   * 
   * @param campaign - Campaign creation data including name and optional status
   * @returns Promise resolving to the newly created campaign with assigned ID
   * @throws Error when validation fails or API request fails
   * 
   * @example
   * ```typescript
   * const newCampaign = await apiService.createCampaign({
   *   name: 'My AR Campaign',
   *   status: 'Draft'
   * });
   * ```
   */
  async createCampaign(campaign: CreateCampaignRequest): Promise<Campaign> {
    return this.request<Campaign>("/campaigns/", {
      method: "POST",
      body: JSON.stringify(campaign),
    });
  }

  /**
   * Updates an existing campaign with partial data changes.
   * Sends PUT request with updated campaign fields to the specific campaign endpoint.
   * 
   * @param id - Unique identifier of the campaign to update
   * @param campaign - Partial campaign data to update (name, creator_id, status)
   * @returns Promise resolving to the updated campaign object
   * @throws Error when campaign not found, validation fails, or API request fails
   * 
   * @example
   * ```typescript
   * const updatedCampaign = await apiService.updateCampaign('campaign-123', {
   *   name: 'Updated Campaign Name',
   *   status: 'Running'
   * });
   * ```
   */
  async updateCampaign(
    id: string,
    campaign: UpdateCampaignRequest,
  ): Promise<Campaign> {
    return this.request<Campaign>(`/campaigns/${id}`, {
      method: "PUT",
      body: JSON.stringify(campaign),
    });
  }

  /**
   * Updates only the status field of a specific campaign.
   * Sends PATCH request to the campaign status endpoint for targeted status changes.
   * 
   * @param id - Unique identifier of the campaign to update
   * @param statusUpdate - Status update object containing the new status value
   * @returns Promise resolving to the updated campaign object
   * @throws Error when campaign not found, invalid status, or API request fails
   * 
   * @example
   * ```typescript
   * const campaign = await apiService.updateCampaignStatus('campaign-123', {
   *   status: 'Paused'
   * });
   * ```
   */
  async updateCampaignStatus(
    id: string,
    statusUpdate: UpdateStatusRequest,
  ): Promise<Campaign> {
    return this.request<Campaign>(`/campaigns/${id}/status`, {
      method: "PATCH",
      body: JSON.stringify(statusUpdate),
    });
  }

  /**
   * Permanently deletes a campaign and all associated data.
   * Sends DELETE request to remove the campaign from the system.
   * 
   * @param id - Unique identifier of the campaign to delete
   * @returns Promise resolving to void when deletion is successful
   * @throws Error when campaign not found, deletion not permitted, or API request fails
   * 
   * @example
   * ```typescript
   * await apiService.deleteCampaign('campaign-123');
   * console.log('Campaign deleted successfully');
   * ```
   */
  async deleteCampaign(id: string): Promise<void> {
    await this.request<void>(`/campaigns/${id}`, {
      method: "DELETE",
    });
  }

  /**
   * Makes an authenticated HTTP request specifically for file upload operations.
   * Handles multipart/form-data requests without setting Content-Type header (let browser set it).
   * 
   * @param endpoint - API endpoint path for file operations
   * @param options - Fetch request options, typically containing FormData body
   * @returns Promise resolving to typed response data
   * @throws Error when request fails, file too large, or invalid file type
   * @private
   */
  private async requestFile<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const token = this.getToken();
    const headers = {
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      let errorMessage = `API Error: ${response.status} ${response.statusText}`;
      try {
        const text = await response.text();
        if (text) {
          errorMessage += ` - ${text}`;
        }
      } catch (e) {
        // Ignore errors when reading error response
      }
      throw new Error(errorMessage);
    }

    const text = await response.text();
    if (!text) {
      throw new Error('Empty response from server');
    }
    
    try {
      return JSON.parse(text);
    } catch (e) {
      throw new Error('Invalid JSON response from server');
    }
  }

  /**
   * Retrieves a paginated list of assets from the API.
   * Supports pagination with configurable page size and number.
   * 
   * @param page - Page number to retrieve (1-based indexing)
   * @param size - Number of assets per page
   * @returns Promise resolving to asset list with pagination metadata
   * @throws Error when API request fails or returns invalid data
   * 
   * @example
   * ```typescript
   * const assets = await apiService.getAssets(1, 25);
   * console.log(`Total assets: ${assets.total}`);
   * ```
   */
  async getAssets(
    page: number = 1,
    size: number = 10,
  ): Promise<AssetListResponse> {
    return this.request<AssetListResponse>(
      `/assets/?page=${page}&size=${size}`,
    );
  }

  /**
   * Uploads a file asset to a specific campaign.
   * Sends multipart/form-data POST request with the file to the upload endpoint.
   * 
   * @param file - File object to upload (image, model, etc.)
   * @param campaignId - ID of the campaign to associate this asset with
   * @returns Promise resolving to the created asset object with metadata
   * @throws Error when file too large, unsupported type, campaign not found, or upload fails
   * 
   * @example
   * ```typescript
   * const fileInput = document.getElementById('file') as HTMLInputElement;
   * const file = fileInput.files[0];
   * const asset = await apiService.uploadAsset(file, 'campaign-123');
   * ```
   */
  async uploadAsset(file: File, campaignId: string): Promise<Asset> {
    const formData = new FormData();
    formData.append("file", file);

    return this.requestFile<Asset>(`/assets/upload/${campaignId}`, {
      method: "POST",
      body: formData,
    });
  }

  /**
   * Permanently deletes an asset and removes associated files from storage.
   * Sends DELETE request to remove the asset from the system.
   * 
   * @param id - Unique identifier of the asset to delete
   * @returns Promise resolving to void when deletion is successful
   * @throws Error when asset not found, deletion not permitted, or API request fails
   * 
   * @example
   * ```typescript
   * await apiService.deleteAsset('asset-456');
   * console.log('Asset deleted successfully');
   * ```
   */
  async deleteAsset(id: string): Promise<void> {
    await this.request<void>(`/assets/${id}`, {
      method: "DELETE",
    });
  }

  /**
   * Retrieves the list of available 3D models for AR rendering.
   * Returns hardcoded model list to ensure consistency with ARRenderer capabilities.
   * 
   * @returns Promise resolving to array of available model names
   * 
   * @example
   * ```typescript
   * const models = await apiService.getModels();
   * console.log('Available models:', models); // ['cube', 'sphere', 'cone', 'littletokyo']
   * ```
   */
  async getModels(): Promise<string[]> {
    // Return available models locally instead of making API call
    // This ensures consistency with ARRenderer model types
    return ['cube', 'sphere', 'cone', 'littletokyo', 'tictactoe'];
  }

  /**
   * Updates the 3D model associated with a specific asset.
   * Sends PATCH request to change which 3D model will be rendered for this asset.
   * 
   * @param assetId - Unique identifier of the asset to update
   * @param modelName - Name of the 3D model to associate (must be from getModels() list)
   * @returns Promise resolving to the updated asset object
   * @throws Error when asset not found, invalid model name, or API request fails
   * 
   * @example
   * ```typescript
   * const updatedAsset = await apiService.updateAssetModel('asset-456', 'cube');
   * console.log('Model updated to:', updatedAsset.model);
   * ```
   */
  async updateAssetModel(assetId: string, modelName: string): Promise<Asset> {
    return this.request<Asset>(`/assets/${assetId}/model`, {
      method: "PATCH",
      body: JSON.stringify({ model: modelName }),
    });
  }

  /**
   * Matches a camera frame against assets in a campaign for AR object placement.
   * Sends image blob to pose estimation service to find matching reference assets.
   * 
   * @param campaignId - ID of campaign containing assets to match against
   * @param imageBlob - Camera frame image as Blob for pose comparison
   * @returns Promise resolving to match result with asset data and similarity score
   * @throws Error when campaign not found, image processing fails, or API request fails
   * 
   * @example
   * ```typescript
   * const canvas = document.getElementById('arCanvas') as HTMLCanvasElement;
   * canvas.toBlob(async (blob) => {
   *   const match = await apiService.matchFrame('campaign-123', blob);
   *   if (match.result.matched) {
   *     console.log('Matched asset:', match.result.asset.filename);
   *   }
   * });
   * ```
   */
  async matchFrame(
    campaignId: string,
    imageBlob: Blob,
  ): Promise<MatchFrameResponse> {
    const formData = new FormData();
    formData.append("frame_image", imageBlob, "frame.jpg");

    return this.requestFile<MatchFrameResponse>(
      `/assets/match-frame/${campaignId}?threshold=0.5`,
      {
        method: "POST",
        body: formData,
      },
    );
  }
}

export const apiService = new ApiService();
