/**
 * @fileoverview Pose estimation service using OpenCV.js for computer vision tasks.
 * Provides feature detection, keypoint matching, and 6DOF pose estimation capabilities
 * for AR applications. Supports multiple feature detectors (ORB, AKAZE, SIFT, SURF)
 * and robust pose solving algorithms including PnP and PnP RANSAC.
 * 
 * <AUTHOR> Platform
 * @version 1.0.0
 */

import {
  KeyPoint,
  Descriptor,
  PoseKeypoints,
  Pose3D,
  PoseSimilarityScore,
  PoseMatchResult,
  KeypointMatch,
  FeatureCorrespondence,
  PoseEstimationConfig,
  CameraIntrinsics,
  Vector3D,
  PoseAnalysisResult,
  PoseQualityMetrics,
  PoseEstimationError,
} from "../types/pose";

// OpenCV.js TypeScript declarations
declare global {
  interface Window {
    cv: any;
  }
}

interface OpenCVMat {
  rows: number;
  cols: number;
  data: any;
  data64F: Float64Array;
  data32F: Float32Array;
  data8U: Uint8Array;
  delete(): void;
}

// OpenCV KeyPoint interface - commented out as it's not used directly
// interface OpenCVKeyPoint {
//   pt: { x: number; y: number };
//   angle: number;
//   size: number;
//   response: number;
//   octave: number;
//   class_id: number;
// }

/**
 * Service class providing comprehensive pose estimation capabilities using OpenCV.js.
 * Handles feature detection, keypoint matching, and 6DOF pose estimation for AR applications.
 * Supports multiple feature detection algorithms and robust pose solving methods.
 * 
 * @public
 */
export class PoseEstimationService {
  private cv: any = null;
  private isInitialized: boolean = false;
  private config: PoseEstimationConfig;
  private initPromise: Promise<void> | null = null;

  /**
   * Creates a new PoseEstimationService instance with configurable parameters.
   * Initializes with optimized default settings for real-time AR applications.
   * 
   * @param config - Optional configuration overrides for pose estimation parameters
   * @param config.detection_threshold - Minimum keypoint detection threshold (0-1, default: 0.01)
   * @param config.max_keypoints - Maximum number of keypoints to detect (default: 250)
   * @param config.feature_type - Feature detector type ("ORB", "AKAZE", "SIFT", "SURF", default: "ORB")
   * @param config.matching_method - Feature matching method ("brute_force", "flann", default: "brute_force")
   * @param config.pose_solver - Pose solving algorithm ("pnp_ransac", "pnp", default: "pnp_ransac")
   * @param config.ransac_threshold - RANSAC inlier threshold in pixels (default: 3.0)
   * @param config.max_iterations - Maximum RANSAC iterations (default: 400)
   * 
   * @example
   * ```typescript
   * // Create service with default settings
   * const poseService = new PoseEstimationService();
   * 
   * // Create service with custom configuration
   * const customService = new PoseEstimationService({
   *   feature_type: "AKAZE",
   *   max_keypoints: 500,
   *   ransac_threshold: 2.0
   * });
   * ```
   */
  constructor(config?: Partial<PoseEstimationConfig>) {
    this.config = {
      detection_threshold: 0.01, // Back to original for better detection
      max_keypoints: 250, // Balanced between performance and accuracy
      feature_type: "ORB",
      matching_method: "brute_force",
      pose_solver: "pnp_ransac",
      ransac_threshold: 3.0, // Back to more strict threshold
      max_iterations: 400, // Increased for better pose estimation
      ...config,
    };
  }

  /**
   * Initializes the OpenCV.js library for pose estimation operations.\n   * Downloads and loads OpenCV.js from CDN if not already available.\n   * Handles singleton initialization to prevent multiple loads.\n   * \n   * @returns Promise that resolves when OpenCV.js is fully initialized and ready\n   * @throws Error when OpenCV.js fails to load or initialize\n   * \n   * @example\n   * ```typescript\n   * const poseService = new PoseEstimationService();\n   * try {\n   *   await poseService.initialize();\n   *   console.log(\"OpenCV.js ready for pose estimation\");\n   * } catch (error) {\n   *   console.error(\"Failed to initialize OpenCV.js:\", error);\n   * }\n   * ```\n   * \n   * @public
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = new Promise((resolve, reject) => {
      if (typeof window !== "undefined" && window.cv) {
        this.cv = window.cv;
        this.isInitialized = true;
        resolve();
        return;
      }

      // Load OpenCV.js if not already loaded
      const script = document.createElement("script");
      script.src = "https://docs.opencv.org/4.8.0/opencv.js";
      script.async = true;

      script.onload = () => {
        const checkOpenCV = () => {
          if (
            window.cv &&
            window.cv.imread &&
            window.cv.Mat &&
            window.cv.BFMatcher
          ) {
            this.cv = window.cv;
            this.isInitialized = true;
            resolve();
          } else {
            setTimeout(checkOpenCV, 100);
          }
        };
        checkOpenCV();
      };

      script.onerror = () => {
        reject(new Error("Failed to load OpenCV.js"));
      };

      document.head.appendChild(script);
    });

    return this.initPromise;
  }

  /**
   * Updates the pose estimation configuration with new parameters.\n   * Merges provided configuration with existing settings, allowing partial updates.\n   * Configuration changes take effect on subsequent operations.\n   * \n   * @param newConfig - Partial configuration object with parameters to update\n   * @param newConfig.detection_threshold - New keypoint detection threshold (0-1)\n   * @param newConfig.max_keypoints - New maximum number of keypoints to detect\n   * @param newConfig.feature_type - New feature detector type\n   * @param newConfig.matching_method - New feature matching method\n   * @param newConfig.pose_solver - New pose solving algorithm\n   * @param newConfig.ransac_threshold - New RANSAC inlier threshold in pixels\n   * @param newConfig.max_iterations - New maximum RANSAC iterations\n   * \n   * @example\n   * ```typescript\n   * const poseService = new PoseEstimationService();\n   * \n   * // Update specific parameters\n   * poseService.updateConfig({\n   *   max_keypoints: 500,\n   *   ransac_threshold: 2.0\n   * });\n   * \n   * // Switch to different feature detector\n   * poseService.updateConfig({ feature_type: \"AKAZE\" });\n   * ```\n   * \n   * @public
   */
  updateConfig(newConfig: Partial<PoseEstimationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Converts HTML image elements to OpenCV Mat format for processing.\n   * Handles HTMLImageElement, HTMLCanvasElement, and HTMLVideoElement inputs.\n   * Creates a canvas intermediate for pixel data extraction.\n   * \n   * @param image - Input image element to convert\n   * @returns OpenCV Mat object containing image data in RGBA format\n   * @throws Error when OpenCV is not initialized\n   * \n   * @private
   */
  private imageToMat(
    image: HTMLImageElement | HTMLCanvasElement | HTMLVideoElement,
  ): OpenCVMat {
    if (!this.cv) throw new Error("OpenCV not initialized");

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d")!;

    canvas.width = image.width || (image as HTMLVideoElement).videoWidth;
    canvas.height = image.height || (image as HTMLVideoElement).videoHeight;

    if (
      image instanceof HTMLImageElement ||
      image instanceof HTMLCanvasElement
    ) {
      ctx.drawImage(image, 0, 0);
    } else if (image instanceof HTMLVideoElement) {
      ctx.drawImage(image, 0, 0, canvas.width, canvas.height);
    }

    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    return this.cv.matFromImageData(imageData);
  }

  /**
   * Creates a feature detector instance based on current configuration.\n   * Supports ORB, AKAZE, SIFT, and SURF detectors with fallback to ORB.\n   * Each detector has different characteristics for speed vs accuracy trade-offs.\n   * \n   * @returns OpenCV feature detector instance (ORB, AKAZE, SIFT, or SURF)\n   * @throws Error when OpenCV is not initialized\n   * \n   * @remarks\n   * - ORB: Fast, binary descriptors, good for real-time applications\n   * - AKAZE: Good balance of speed and accuracy, binary descriptors\n   * - SIFT: High accuracy, floating-point descriptors, slower\n   * - SURF: Fast, floating-point descriptors, patented algorithm\n   * \n   * @private
   */
  private createFeatureDetector(): any {
    if (!this.cv) throw new Error("OpenCV not initialized");

    switch (this.config.feature_type) {
      case "ORB":
        return new this.cv.ORB(this.config.max_keypoints);
      case "AKAZE":
        return new this.cv.AKAZE();
      case "SIFT":
        return this.cv.SIFT && new this.cv.SIFT(this.config.max_keypoints);
      case "SURF":
        return this.cv.SURF && new this.cv.SURF();
      default:
        return new this.cv.ORB(this.config.max_keypoints);
    }
  }

  /**
   * Creates a feature matcher instance based on current configuration.\n   * Automatically selects appropriate normalization type for descriptor matching.\n   * Supports brute force and FLANN-based matching with fallback mechanisms.\n   * \n   * @returns OpenCV feature matcher instance (BFMatcher or FlannBasedMatcher)\n   * @throws Error when OpenCV is not initialized or matcher creation fails\n   * \n   * @remarks\n   * - Binary descriptors (ORB, AKAZE) use Hamming distance\n   * - Floating-point descriptors (SIFT, SURF) use L2 distance\n   * - Brute force matcher is more accurate but slower\n   * - FLANN matcher is faster but may be less accurate\n   * \n   * @private
   */
  private createFeatureMatcher(): any {
    if (!this.cv) {
      throw new Error("OpenCV not initialized");
    }

    try {
      const normType =
        this.config.feature_type === "ORB" ||
        this.config.feature_type === "AKAZE"
          ? this.cv.NORM_HAMMING
          : this.cv.NORM_L2;

      switch (this.config.matching_method) {
        case "brute_force":
          if (!this.cv.BFMatcher) {
            throw new Error("BFMatcher not available in OpenCV.js");
          }
          return new this.cv.BFMatcher(normType, false);
        case "flann":
          if (!this.cv.FlannBasedMatcher) {
            return new this.cv.BFMatcher(normType, false);
          }
          return new this.cv.FlannBasedMatcher();
        default:
          return new this.cv.BFMatcher(normType, false);
      }
    } catch (error) {
      throw new Error(`Failed to create feature matcher: ${error}`);
    }
  }

  /**
   * Detects keypoints and computes descriptors from an input image.\n   * Performs feature detection using the configured algorithm (ORB, AKAZE, etc.).\n   * Converts image to grayscale, detects features, and returns structured results.\n   * \n   * @param image - Input image element (HTMLImageElement, HTMLCanvasElement, or HTMLVideoElement)\n   * @returns Promise resolving to PoseKeypoints containing detected features and metadata\n   * @returns PoseKeypoints.keypoints - Array of detected keypoint coordinates and properties\n   * @returns PoseKeypoints.descriptors - Array of feature descriptors for matching\n   * @returns PoseKeypoints.image_width - Width of the processed image\n   * @returns PoseKeypoints.image_height - Height of the processed image\n   * @returns PoseKeypoints.detection_confidence - Confidence score based on keypoint density\n   * @returns PoseKeypoints.feature_count - Total number of detected features\n   * @throws Error when image processing fails or OpenCV operations error\n   * \n   * @example\n   * ```typescript\n   * const poseService = new PoseEstimationService();\n   * await poseService.initialize();\n   * \n   * const image = document.getElementById('myImage') as HTMLImageElement;\n   * const keypoints = await poseService.detectKeypoints(image);\n   * \n   * console.log(`Detected ${keypoints.feature_count} keypoints`);\n   * console.log(`Detection confidence: ${keypoints.detection_confidence}`);\n   * ```\n   * \n   * @public
   */
  async detectKeypoints(
    image: HTMLImageElement | HTMLCanvasElement | HTMLVideoElement,
  ): Promise<PoseKeypoints> {
    await this.initialize();

    const imageMat = this.imageToMat(image);
    const grayMat = new this.cv.Mat();

    try {
      // Convert to grayscale
      this.cv.cvtColor(imageMat, grayMat, this.cv.COLOR_RGBA2GRAY);

      // Create detector
      const detector = this.createFeatureDetector();

      // Detect keypoints and compute descriptors
      const keypoints = new this.cv.KeyPointVector();
      const descriptors = new this.cv.Mat();

      detector.detectAndCompute(
        grayMat,
        new this.cv.Mat(),
        keypoints,
        descriptors,
      );

      // Convert to our format
      const keypointArray: KeyPoint[] = [];
      for (let i = 0; i < keypoints.size(); i++) {
        const kp = keypoints.get(i);
        keypointArray.push({
          x: kp.pt.x,
          y: kp.pt.y,
          angle: kp.angle,
          size: kp.size,
          response: kp.response,
          octave: kp.octave,
          class_id: kp.class_id,
        });
      }

      // Convert descriptors
      const descriptorArray: Descriptor[] = [];
      if (descriptors.rows > 0) {
        for (let i = 0; i < descriptors.rows; i++) {
          const desc = descriptors.row(i);
          const data: number[] = [];
          for (let j = 0; j < desc.cols; j++) {
            data.push(desc.data[j]);
          }
          descriptorArray.push({ data, length: data.length });
          desc.delete();
        }
      }

      const result: PoseKeypoints = {
        keypoints: keypointArray,
        descriptors: descriptorArray,
        image_width: imageMat.cols,
        image_height: imageMat.rows,
        detection_confidence: Math.min(
          keypointArray.length / this.config.max_keypoints,
          1.0,
        ),
        feature_count: keypointArray.length,
      };

      // Cleanup
      detector.delete();
      keypoints.delete();
      descriptors.delete();

      return result;
    } finally {
      imageMat.delete();
      grayMat.delete();
    }
  }

  /**
   * Matches keypoints between two sets of detected features using descriptor similarity.\n   * Performs feature matching with distance-based filtering and confidence scoring.\n   * Uses the configured matching algorithm (brute force or FLANN) with timeout protection.\n   * \n   * @param keypoints1 - First set of keypoints to match (query image)\n   * @param keypoints2 - Second set of keypoints to match (reference/training image)\n   * @param threshold - Maximum descriptor distance for valid matches (default: 50)\n   * @returns Promise resolving to FeatureCorrespondence with match results\n   * @returns FeatureCorrespondence.matches - Array of valid keypoint matches with confidence scores\n   * @returns FeatureCorrespondence.inlier_count - Number of matches passing the threshold\n   * @returns FeatureCorrespondence.outlier_count - Number of matches rejected by threshold\n   * @throws Error when matching fails, times out, or OpenCV operations error\n   * \n   * @example\n   * ```typescript\n   * const poseService = new PoseEstimationService();\n   * await poseService.initialize();\n   * \n   * const currentKeypoints = await poseService.detectKeypoints(currentImage);\n   * const referenceKeypoints = await poseService.detectKeypoints(referenceImage);\n   * \n   * const correspondence = await poseService.matchKeypoints(\n   *   currentKeypoints,\n   *   referenceKeypoints,\n   *   75 // Custom threshold\n   * );\n   * \n   * console.log(`Found ${correspondence.inlier_count} good matches`);\n   * console.log(`Rejected ${correspondence.outlier_count} poor matches`);\n   * ```\n   * \n   * @public
   */
  async matchKeypoints(
    keypoints1: PoseKeypoints,
    keypoints2: PoseKeypoints,
    threshold: number = 50,
  ): Promise<FeatureCorrespondence> {
    await this.initialize();

    if (
      keypoints1.descriptors.length === 0 ||
      keypoints2.descriptors.length === 0
    ) {
      return {
        matches: [],
        inlier_count: 0,
        outlier_count: 0,
      };
    }

    try {
      // Convert descriptors back to OpenCV format
      const desc1 = this.descriptorsToMat(keypoints1.descriptors);
      const desc2 = this.descriptorsToMat(keypoints2.descriptors);

      // Create matcher
      const matcher = this.createFeatureMatcher();

      // Match descriptors
      const matches = new this.cv.DMatchVector();

      const matchPromise = new Promise<void>((resolve, reject) => {
        try {
          matcher.match(desc1, desc2, matches);
          resolve();
        } catch (error) {
          reject(error);
        }
      });

      const timeoutPromise = new Promise<void>((_, reject) => {
        setTimeout(
          () => reject(new Error("Matcher timeout after 5 seconds")),
          5000,
        );
      });

      try {
        await Promise.race([matchPromise, timeoutPromise]);
      } catch (timeoutError) {
        throw timeoutError;
      }

      const totalMatches = matches.size();
      const goodMatches: KeypointMatch[] = [];

      for (let i = 0; i < totalMatches; i++) {
        const match = matches.get(i);

        if (match.distance < threshold) {
          const queryIdx = match.queryIdx;
          const trainIdx = match.trainIdx;

          if (
            queryIdx >= 0 &&
            queryIdx < keypoints1.keypoints.length &&
            trainIdx >= 0 &&
            trainIdx < keypoints2.keypoints.length
          ) {
            const confidence = Math.max(0, 1 - match.distance / threshold);
            const matchType =
              match.distance < threshold * 0.7 ? "exact" : "approximate";

            goodMatches.push({
              query_keypoint: keypoints1.keypoints[queryIdx],
              reference_keypoint: keypoints2.keypoints[trainIdx],
              distance: match.distance,
              confidence: confidence,
              match_type: matchType,
            });
          }
        }
      }

      // Cleanup
      desc1.delete();
      desc2.delete();
      matcher.delete();
      matches.delete();

      const result = {
        matches: goodMatches,
        inlier_count: goodMatches.length,
        outlier_count: Math.max(0, totalMatches - goodMatches.length),
      };

      return result;
    } catch (error) {
      throw new Error(`Keypoint matching failed: ${error}`);
    }
  }

  /**
   * Estimates 6DOF camera pose from feature correspondences using PnP algorithms.\n   * Solves the Perspective-n-Point problem to determine camera position and orientation.\n   * Uses calibrated camera parameters and assumes planar reference object at z=0.\n   * \n   * @param correspondence - Feature correspondences between current and reference images\n   * @param cameraIntrinsics - Calibrated camera parameters (focal length, principal point, distortion)\n   * @param cameraIntrinsics.focal_length - Camera focal length in pixels {x, y}\n   * @param cameraIntrinsics.principal_point - Principal point coordinates {x, y}\n   * @param cameraIntrinsics.distortion_coefficients - Lens distortion coefficients array\n   * @param cameraIntrinsics.image_size - Image dimensions {x: width, y: height}\n   * @returns Promise resolving to Pose3D object with position, rotation, and confidence, or null if failed\n   * @returns Pose3D.translation - 3D position vector in meters\n   * @returns Pose3D.rotation - Rotation quaternion {x, y, z, w}\n   * @returns Pose3D.scale - Scale factors (always {x:1, y:1, z:1} for rigid objects)\n   * @returns Pose3D.confidence - Pose estimation confidence (0-1) based on match quality\n   * @throws Error when pose estimation fails or insufficient matches provided\n   * \n   * @example\n   * ```typescript\n   * const poseService = new PoseEstimationService();\n   * await poseService.initialize();\n   * \n   * const correspondence = await poseService.matchKeypoints(current, reference);\n   * const cameraParams = {\n   *   focal_length: { x: 800, y: 800 },\n   *   principal_point: { x: 320, y: 240 },\n   *   distortion_coefficients: [0.1, -0.2, 0.01, 0.005, 0],\n   *   image_size: { x: 640, y: 480 }\n   * };\n   * \n   * const pose = await poseService.estimatePose(correspondence, cameraParams);\n   * if (pose) {\n   *   console.log(`Camera at: (${pose.translation.x}, ${pose.translation.y}, ${pose.translation.z})`);\n   *   console.log(`Confidence: ${pose.confidence}`);\n   * }\n   * ```\n   * \n   * @public
   */
  async estimatePose(
    correspondence: FeatureCorrespondence,
    cameraIntrinsics: CameraIntrinsics,
  ): Promise<Pose3D | null> {
    await this.initialize();

    // Need at least 4 good matches for pose estimation
    if (correspondence.matches.length < 4) {
      return null;
    }

    try {
      // Prepare 2D and 3D points
      const imagePoints: number[] = [];
      const objectPoints: number[] = [];

      correspondence.matches.forEach((match) => {
        // Image points (2D)
        imagePoints.push(match.query_keypoint.x, match.query_keypoint.y);

        // Object points (3D) - assuming planar object at z=0
        // Use a more reasonable scale: 1 pixel = 1mm (0.001m) for better numerical stability
        const worldX = (match.reference_keypoint.x - cameraIntrinsics.image_size.x / 2) * 0.001;
        const worldY = (match.reference_keypoint.y - cameraIntrinsics.image_size.y / 2) * 0.001;
        const worldZ = 0; // Planar object
        objectPoints.push(worldX, worldY, worldZ);
      });

      // Create OpenCV matrices
      const imagePointsMat = this.cv.matFromArray(
        correspondence.matches.length,
        1,
        this.cv.CV_32FC2,
        imagePoints,
      );

      const objectPointsMat = this.cv.matFromArray(
        correspondence.matches.length,
        1,
        this.cv.CV_32FC3,
        objectPoints,
      );

      // Camera matrix
      const cameraMatrix = this.cv.matFromArray(3, 3, this.cv.CV_64F, [
        cameraIntrinsics.focal_length.x,
        0,
        cameraIntrinsics.principal_point.x,
        0,
        cameraIntrinsics.focal_length.y,
        cameraIntrinsics.principal_point.y,
        0,
        0,
        1,
      ]);

      // Distortion coefficients
      const distCoeffs = this.cv.matFromArray(
        cameraIntrinsics.distortion_coefficients.length,
        1,
        this.cv.CV_64F,
        cameraIntrinsics.distortion_coefficients,
      );

      // Solve PnP with error handling
      const rvec = new this.cv.Mat();
      const tvec = new this.cv.Mat();

      let success = false;
      try {
        // Try regular PnP first (faster than RANSAC)
        success = this.cv.solvePnP(
          objectPointsMat,
          imagePointsMat,
          cameraMatrix,
          distCoeffs,
          rvec,
          tvec,
        );
        
        // If regular PnP fails, try RANSAC
        if (!success && this.config.pose_solver === "pnp_ransac") {
          const inliers = new this.cv.Mat();
          success = this.cv.solvePnPRansac(
            objectPointsMat,
            imagePointsMat,
            cameraMatrix,
            distCoeffs,
            rvec,
            tvec,
            false,
            this.config.max_iterations,
            this.config.ransac_threshold,
            0.99,
            inliers,
          );
          inliers.delete();
        }
      } catch (pnpError) {
        console.error("PnP solver error:", pnpError);
        rvec.delete();
        tvec.delete();
        imagePointsMat.delete();
        objectPointsMat.delete();
        cameraMatrix.delete();
        distCoeffs.delete();
        return null;
      }

      if (!success) {
        rvec.delete();
        tvec.delete();
        imagePointsMat.delete();
        objectPointsMat.delete();
        cameraMatrix.delete();
        distCoeffs.delete();
        return null;
      }

      // Extract pose
      const translation: Vector3D = {
        x: tvec.data64F[0],
        y: tvec.data64F[1],
        z: tvec.data64F[2],
      };

      const rotation = this.rodriguesToQuaternion(rvec);

      // Calculate confidence based on number of matches and their quality
      const matchQuality = correspondence.inlier_count / Math.max(correspondence.matches.length, 1);
      const matchCount = Math.min(correspondence.matches.length / 10, 1); // Scale to 1 at 10+ matches
      const confidence = (matchQuality * 0.7 + matchCount * 0.3); // Weighted combination

      // Cleanup
      imagePointsMat.delete();
      objectPointsMat.delete();
      cameraMatrix.delete();
      distCoeffs.delete();
      rvec.delete();
      tvec.delete();

      const finalPose = {
        translation,
        rotation,
        scale: { x: 1, y: 1, z: 1 },
        confidence: Math.max(0, Math.min(1, confidence)),
      };

      return finalPose;
    } catch (error) {
      throw new Error(`Pose estimation failed: ${error}`);
    }
  }

  /**
   * Calculates comprehensive similarity metrics between two sets of pose keypoints.\n   * Combines keypoint count similarity, descriptor matching quality, and geometric consistency.\n   * Provides detailed breakdown of similarity components for analysis.\n   * \n   * @param pose1 - First pose keypoints for comparison\n   * @param pose2 - Second pose keypoints for comparison\n   * @param correspondence - Feature correspondences between the poses\n   * @returns PoseSimilarityScore containing detailed similarity metrics\n   * @returns PoseSimilarityScore.overall_score - Combined similarity score (0-1)\n   * @returns PoseSimilarityScore.keypoint_similarity - Similarity based on keypoint count ratio\n   * @returns PoseSimilarityScore.descriptor_similarity - Average confidence of matched descriptors\n   * @returns PoseSimilarityScore.geometric_similarity - Geometric consistency of matches\n   * @returns PoseSimilarityScore.confidence - Overall confidence considering detection quality\n   * @returns PoseSimilarityScore.matched_keypoints - Number of successfully matched keypoints\n   * @returns PoseSimilarityScore.total_keypoints - Maximum keypoints between the two poses\n   * \n   * @example\n   * ```typescript\n   * const similarity = poseService.calculateSimilarity(\n   *   currentKeypoints,\n   *   referenceKeypoints,\n   *   correspondence\n   * );\n   * \n   * console.log(`Overall similarity: ${similarity.overall_score}`);\n   * console.log(`Keypoint similarity: ${similarity.keypoint_similarity}`);\n   * console.log(`Geometric consistency: ${similarity.geometric_similarity}`);\n   * ```\n   * \n   * @public
   */
  calculateSimilarity(
    pose1: PoseKeypoints,
    pose2: PoseKeypoints,
    correspondence: FeatureCorrespondence,
  ): PoseSimilarityScore {
    const keypointSimilarity =
      Math.min(pose1.keypoints.length, pose2.keypoints.length) /
      Math.max(pose1.keypoints.length, pose2.keypoints.length);
    const descriptorSimilarity =
      correspondence.matches.length > 0
        ? correspondence.matches.reduce(
            (sum, match) => sum + match.confidence,
            0,
          ) / correspondence.matches.length
        : 0;

    const geometricSimilarity =
      correspondence.inlier_count / Math.max(correspondence.matches.length, 1);
    const overallScore =
      (keypointSimilarity + descriptorSimilarity + geometricSimilarity) / 3;
    return {
      overall_score: overallScore,
      keypoint_similarity: keypointSimilarity,
      descriptor_similarity: descriptorSimilarity,
      geometric_similarity: geometricSimilarity,
      confidence: Math.min(
        overallScore,
        pose1.detection_confidence,
        pose2.detection_confidence,
      ),
      matched_keypoints: correspondence.matches.length,
      total_keypoints: Math.max(pose1.keypoints.length, pose2.keypoints.length),
    };
  }

  /**
   * Performs complete pose analysis using pre-detected keypoints to avoid re-detection.\n   * Optimized for real-time applications where keypoints are already available.\n   * Combines feature matching, similarity analysis, and pose estimation in one call.\n   * \n   * @param currentKeypoints - Pre-detected keypoints from current frame/image\n   * @param referenceKeypoints - Optional reference keypoints for matching and pose estimation\n   * @param cameraIntrinsics - Optional camera calibration for 6DOF pose estimation\n   * @returns Promise resolving to comprehensive PoseAnalysisResult\n   * @returns PoseAnalysisResult.pose - 6DOF pose estimate (null if no camera intrinsics or insufficient matches)\n   * @returns PoseAnalysisResult.keypoints - Current frame keypoints used for analysis\n   * @returns PoseAnalysisResult.match_result - Keypoint matching results and quality metrics\n   * @returns PoseAnalysisResult.quality_metrics - Image and pose quality assessment\n   * @returns PoseAnalysisResult.processing_stats - Performance timing breakdown\n   * @returns PoseAnalysisResult.error - Error information if analysis failed\n   * \n   * @example\n   * ```typescript\n   * const poseService = new PoseEstimationService();\n   * await poseService.initialize();\n   * \n   * // Pre-detect keypoints from video frame\n   * const currentKeypoints = await poseService.detectKeypoints(videoFrame);\n   * \n   * // Analyze against reference with camera calibration\n   * const result = await poseService.analyzePoseFromKeypoints(\n   *   currentKeypoints,\n   *   referenceKeypoints,\n   *   cameraIntrinsics\n   * );\n   * \n   * if (result.match_result?.matched) {\n   *   console.log(`Match quality: ${result.match_result.match_quality}`);\n   *   if (result.pose) {\n   *     console.log(`6DOF pose confidence: ${result.pose.confidence}`);\n   *   }\n   * }\n   * console.log(`Processing took ${result.processing_stats.total_time_ms}ms`);\n   * ```\n   * \n   * @public
   */
  async analyzePoseFromKeypoints(
    currentKeypoints: PoseKeypoints,
    referenceKeypoints?: PoseKeypoints,
    cameraIntrinsics?: CameraIntrinsics,
  ): Promise<PoseAnalysisResult> {
    const startTime = performance.now();

    try {
      let matchResult: PoseMatchResult | null = null;
      let pose: Pose3D | null = null;
      let matchingTime = 0;
      let poseEstimationTime = 0;

      if (referenceKeypoints) {
        // Match keypoints
        const matchingStart = performance.now();
        const correspondence = await this.matchKeypoints(
          currentKeypoints,
          referenceKeypoints,
        );
        matchingTime = performance.now() - matchingStart;

        // Calculate similarity
        const similarity = this.calculateSimilarity(
          currentKeypoints,
          referenceKeypoints,
          correspondence,
        );

        matchResult = {
          matched: similarity.overall_score > 0.5 && correspondence.matches.length >= 6, // Stricter validation
          similarity_score: similarity,
          threshold_used: 0.5,
          processing_time_ms: matchingTime,
          match_quality: this.getMatchQuality(similarity.overall_score),
        };

        // Estimate pose if camera intrinsics provided and match is valid
        if (cameraIntrinsics && matchResult.matched && correspondence.matches.length >= 4) {
          const poseStart = performance.now();
          pose = await this.estimatePose(correspondence, cameraIntrinsics);
          poseEstimationTime = performance.now() - poseStart;
        }
      }

      // Calculate quality metrics
      const qualityMetrics: PoseQualityMetrics = {
        keypoint_visibility: currentKeypoints.detection_confidence,
        motion_blur: 0.8, // Placeholder - would need actual blur detection
        illumination_quality: 0.9, // Placeholder - would need actual illumination analysis
        occlusion_level: 0.1, // Placeholder - would need actual occlusion detection
        overall_quality: currentKeypoints.detection_confidence,
      };

      const totalTime = performance.now() - startTime;

      return {
        pose,
        keypoints: currentKeypoints,
        match_result: matchResult,
        quality_metrics: qualityMetrics,
        processing_stats: {
          detection_time_ms: 0, // No detection time since keypoints were pre-detected
          matching_time_ms: matchingTime,
          pose_estimation_time_ms: poseEstimationTime,
          total_time_ms: totalTime,
        },
      };
    } catch (error) {
      const poseError: PoseEstimationError = {
        error_type: "geometric_inconsistency",
        error_message: error instanceof Error ? error.message : "Unknown error",
        suggested_actions: [
          "Check camera calibration",
          "Improve lighting conditions",
          "Ensure target is visible",
        ],
        error_code: 1001,
      };

      return {
        pose: null,
        keypoints: {
          keypoints: [],
          descriptors: [],
          image_width: 0,
          image_height: 0,
          detection_confidence: 0,
          feature_count: 0,
        },
        match_result: null,
        quality_metrics: {
          keypoint_visibility: 0,
          motion_blur: 0,
          illumination_quality: 0,
          occlusion_level: 1,
          overall_quality: 0,
        },
        processing_stats: {
          detection_time_ms: 0,
          matching_time_ms: 0,
          pose_estimation_time_ms: 0,
          total_time_ms: performance.now() - startTime,
        },
        error: poseError,
      };
    }
  }

  /**
   * Performs complete end-to-end pose analysis from raw image input.\n   * Combines keypoint detection, feature matching, and pose estimation in a single call.\n   * Includes comprehensive timing and quality metrics for performance monitoring.\n   * \n   * @param image - Input image element for keypoint detection\n   * @param referenceKeypoints - Optional reference keypoints for matching and pose estimation\n   * @param cameraIntrinsics - Optional camera calibration for 6DOF pose estimation\n   * @returns Promise resolving to comprehensive PoseAnalysisResult\n   * @returns PoseAnalysisResult.pose - 6DOF pose estimate (null if no camera intrinsics or insufficient matches)\n   * @returns PoseAnalysisResult.keypoints - Detected keypoints from input image\n   * @returns PoseAnalysisResult.match_result - Keypoint matching results and quality metrics\n   * @returns PoseAnalysisResult.quality_metrics - Image and pose quality assessment\n   * @returns PoseAnalysisResult.processing_stats - Complete performance timing breakdown including detection time\n   * @returns PoseAnalysisResult.error - Error information if analysis failed\n   * \n   * @example\n   * ```typescript\n   * const poseService = new PoseEstimationService();\n   * await poseService.initialize();\n   * \n   * // Complete analysis from raw image\n   * const image = document.getElementById('targetImage') as HTMLImageElement;\n   * const result = await poseService.analyzePose(\n   *   image,\n   *   referenceKeypoints,\n   *   cameraIntrinsics\n   * );\n   * \n   * console.log(`Detected ${result.keypoints.feature_count} keypoints`);\n   * console.log(`Detection took ${result.processing_stats.detection_time_ms}ms`);\n   * \n   * if (result.match_result?.matched && result.pose) {\n   *   console.log('Successful pose tracking!');\n   *   console.log(`6DOF pose: x=${result.pose.translation.x}, y=${result.pose.translation.y}, z=${result.pose.translation.z}`);\n   * }\n   * ```\n   * \n   * @public
   */
  async analyzePose(
    image: HTMLImageElement | HTMLCanvasElement | HTMLVideoElement,
    referenceKeypoints?: PoseKeypoints,
    cameraIntrinsics?: CameraIntrinsics,
  ): Promise<PoseAnalysisResult> {
    const startTime = performance.now();

    try {
      // Detect keypoints
      const detectionStart = performance.now();
      const keypoints = await this.detectKeypoints(image);
      const detectionTime = performance.now() - detectionStart;

      let matchResult: PoseMatchResult | null = null;
      let pose: Pose3D | null = null;
      let matchingTime = 0;
      let poseEstimationTime = 0;
      if (referenceKeypoints) {
        // Match keypoints
        const matchingStart = performance.now();

        const correspondence = await this.matchKeypoints(
          keypoints,
          referenceKeypoints,
        );
        matchingTime = performance.now() - matchingStart;

        // Calculate similarity
        const similarity = this.calculateSimilarity(
          keypoints,
          referenceKeypoints,
          correspondence,
        );

        matchResult = {
          matched: similarity.overall_score > 0.5 && correspondence.matches.length >= 6, // Stricter validation
          similarity_score: similarity,
          threshold_used: 0.5,
          processing_time_ms: matchingTime,
          match_quality: this.getMatchQuality(similarity.overall_score),
        };
        // Estimate pose if camera intrinsics provided and match is valid
        if (cameraIntrinsics && matchResult.matched && correspondence.matches.length >= 4) {
          const poseStart = performance.now();
          pose = await this.estimatePose(correspondence, cameraIntrinsics);
          poseEstimationTime = performance.now() - poseStart;
        }
      }

      // Calculate quality metrics
      const qualityMetrics: PoseQualityMetrics = {
        keypoint_visibility: keypoints.detection_confidence,
        motion_blur: 0.8, // Placeholder - would need actual blur detection
        illumination_quality: 0.9, // Placeholder - would need actual illumination analysis
        occlusion_level: 0.1, // Placeholder - would need actual occlusion detection
        overall_quality: keypoints.detection_confidence,
      };
      const totalTime = performance.now() - startTime;

      return {
        pose,
        keypoints,
        match_result: matchResult,
        quality_metrics: qualityMetrics,
        processing_stats: {
          detection_time_ms: detectionTime,
          matching_time_ms: matchingTime,
          pose_estimation_time_ms: poseEstimationTime,
          total_time_ms: totalTime,
        },
      };
    } catch (error) {
      const poseError: PoseEstimationError = {
        error_type: "geometric_inconsistency",
        error_message: error instanceof Error ? error.message : "Unknown error",
        suggested_actions: [
          "Check camera calibration",
          "Improve lighting conditions",
          "Ensure target is visible",
        ],
        error_code: 1001,
      };

      return {
        pose: null,
        keypoints: {
          keypoints: [],
          descriptors: [],
          image_width: 0,
          image_height: 0,
          detection_confidence: 0,
          feature_count: 0,
        },
        match_result: null,
        quality_metrics: {
          keypoint_visibility: 0,
          motion_blur: 0,
          illumination_quality: 0,
          occlusion_level: 1,
          overall_quality: 0,
        },
        processing_stats: {
          detection_time_ms: 0,
          matching_time_ms: 0,
          pose_estimation_time_ms: 0,
          total_time_ms: performance.now() - startTime,
        },
        error: poseError,
      };
    }
  }

  /**
   * Converts array of feature descriptors to OpenCV Mat format for matching operations.\n   * Validates descriptor consistency and handles empty descriptor arrays gracefully.\n   * Creates properly formatted Mat with correct data type for descriptor matching.\n   * \n   * @param descriptors - Array of feature descriptors to convert\n   * @returns OpenCV Mat containing descriptor data in CV_8U format\n   * @throws Error when OpenCV not initialized, descriptors have inconsistent lengths, or contain invalid data\n   * \n   * @private
   */
  private descriptorsToMat(descriptors: Descriptor[]): OpenCVMat {
    if (!this.cv) {
      throw new Error("OpenCV not initialized in descriptorsToMat");
    }

    if (descriptors.length === 0) {
      return new this.cv.Mat(0, 0, this.cv.CV_8U);
    }

    const rows = descriptors.length;
    const cols = descriptors[0].data.length;

    // Validate all descriptors have the same length
    for (let i = 1; i < descriptors.length; i++) {
      if (descriptors[i].data.length !== cols) {
        throw new Error(
          `Inconsistent descriptor lengths: expected ${cols}, got ${descriptors[i].data.length} at index ${i}`,
        );
      }
    }

    try {
      const data = new Uint8Array(rows * cols);

      for (let i = 0; i < rows; i++) {
        const descriptor = descriptors[i];
        if (!descriptor || !descriptor.data) {
          throw new Error(`Invalid descriptor at index ${i}`);
        }

        for (let j = 0; j < cols; j++) {
          const value = descriptor.data[j];
          if (value === undefined || value === null) {
            throw new Error(`Invalid descriptor value at [${i}][${j}]`);
          }
          data[i * cols + j] = Math.min(255, Math.max(0, Math.round(value)));
        }
      }

      // Create Mat directly from typed array without Array.from conversion
      const mat = new this.cv.Mat(rows, cols, this.cv.CV_8U);
      mat.data.set(data);

      return mat;
    } catch (error) {
      throw new Error(`Failed to create descriptor matrix: ${error}`);
    }
  }

  /**
   * Converts Rodrigues rotation vector to unit quaternion representation.
   * Handles the mathematical conversion from axis-angle to quaternion format.
   * Provides special handling for near-zero rotations to avoid numerical instability.
   * 
   * @param rvec - OpenCV Mat containing Rodrigues rotation vector (3x1)
   * @returns Quaternion object with x, y, z, w components (unit quaternion)
   * 
   * @remarks
   * Uses the standard conversion formula: q = [sin(θ/2) * axis, cos(θ/2)]
   * where θ is the rotation angle and axis is the normalized rotation vector.
   * 
   * @private
   */
  private rodriguesToQuaternion(rvec: OpenCVMat): {
    x: number;
    y: number;
    z: number;
    w: number;
  } {
    const angle = Math.sqrt(
      rvec.data64F[0] * rvec.data64F[0] +
        rvec.data64F[1] * rvec.data64F[1] +
        rvec.data64F[2] * rvec.data64F[2],
    );

    if (angle < 1e-6) {
      return { x: 0, y: 0, z: 0, w: 1 };
    }

    const s = Math.sin(angle / 2) / angle;
    const c = Math.cos(angle / 2);

    return {
      x: rvec.data64F[0] * s,
      y: rvec.data64F[1] * s,
      z: rvec.data64F[2] * s,
      w: c,
    };
  }

  /**
   * Converts numerical similarity score to categorical quality rating.
   * Provides human-readable assessment of keypoint matching performance.
   * Uses fixed thresholds to categorize similarity scores into quality levels.
   * 
   * @param score - Similarity score from 0 to 1
   * @returns Quality category based on score thresholds
   * - \"excellent\": score > 0.8 (very high similarity)
   * - \"good\": score > 0.6 (good similarity)
   * - \"fair\": score > 0.4 (acceptable similarity)
   * - \"poor\": score ≤ 0.4 (low similarity)
   * 
   * @private
   */
  private getMatchQuality(
    score: number,
  ): "excellent" | "good" | "fair" | "poor" {
    if (score > 0.8) return "excellent";
    if (score > 0.6) return "good";
    if (score > 0.4) return "fair";
    return "poor";
  }

  /**
   * Cleans up service resources and resets initialization state.\n   * Should be called when the service is no longer needed to free memory.\n   * After calling destroy(), the service must be re-initialized before use.\n   * \n   * @example\n   * ```typescript\n   * const poseService = new PoseEstimationService();\n   * await poseService.initialize();\n   * \n   * // Use the service...\n   * \n   * // Clean up when done\n   * poseService.destroy();\n   * ```\n   * \n   * @public
   */
  destroy(): void {
    this.isInitialized = false;
    this.cv = null;
    this.initPromise = null;
  }
}

export const poseEstimationService = new PoseEstimationService();
