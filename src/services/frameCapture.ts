import { apiService, MatchFrameResponse } from './api';

/**
 * Configuration interface for frame capture settings.
 * Defines the parameters used to control video frame capture quality and timing.
 */
export interface FrameCaptureConfig {
  /** Width of captured frames in pixels */
  width: number;
  /** Height of captured frames in pixels */
  height: number;
  /** Image quality from 0.1 to 1.0, where 1.0 is highest quality */
  quality: number;
  /** Image format for captured frames */
  format: 'image/jpeg' | 'image/png';
  /** Capture rate in frames per second */
  captureRate: number;
}

/**
 * Result interface for frame capture operations.
 * Contains the captured frame data and metadata.
 */
export interface CaptureResult {
  /** Binary blob containing the captured frame image */
  blob: Blob;
  /** Data URL representation of the captured frame */
  dataUrl: string;
  /** Unix timestamp when the frame was captured */
  timestamp: number;
  /** Sequential frame number in the capture session */
  frameNumber: number;
}

/**
 * Extended result interface for frame matching operations.
 * Combines match response data with capture information.
 */
export interface MatchResult extends MatchFrameResponse {
  /** Additional capture metadata */
  captureInfo: {
    /** Unix timestamp when the frame was captured */
    timestamp: number;
    /** Sequential frame number in the capture session */
    frameNumber: number;
    /** Time taken to process the frame in milliseconds */
    processingTime: number;
  };
}

/**
 * Statistics interface for tracking capture stream performance.
 * Provides metrics about the ongoing capture session.
 */
export interface StreamStats {
  /** Total number of frames processed in the session */
  framesProcessed: number;
  /** Average processing time per frame in milliseconds */
  averageProcessingTime: number;
  /** Timestamp of the last processed frame */
  lastFrameTime: number;
  /** Whether the capture service is currently active */
  isActive: boolean;
  /** Number of successful matches found in the session */
  matchesFound: number;
}

/**
 * Service class for capturing and processing video frames.
 * 
 * The FrameCaptureService provides functionality for capturing frames from a video element,
 * converting them to various formats, and optionally matching them against campaign assets
 * through the API. It supports both manual frame capture and automatic continuous capture
 * with configurable frame rates and quality settings.
 * 
 * @example
 * ```typescript
 * // Create a frame capture service with custom configuration
 * const captureService = new FrameCaptureService({
 *   width: 1280,
 *   height: 720,
 *   quality: 0.9,
 *   captureRate: 15
 * });
 * 
 * // Set up event handlers
 * captureService.onFrame((result) => {
 *   console.log('Frame captured:', result.frameNumber);
 * });
 * 
 * captureService.onMatch((result) => {
 *   if (result.result.matched) {
 *     console.log('Match found:', result.result.asset);
 *   }
 * });
 * 
 * // Start capturing frames
 * captureService.setVideoElement(videoElement);
 * captureService.startCapture();
 * ```
 */
export class FrameCaptureService {
  private video: HTMLVideoElement | null = null;
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private config: FrameCaptureConfig;
  private isCapturing: boolean = false;
  private frameNumber: number = 0;
  private captureInterval: number | null = null;
  private stats: StreamStats;
  private onFrameCallback?: (result: CaptureResult) => void;
  private onMatchCallback?: (result: MatchResult) => void;
  private onErrorCallback?: (error: Error) => void;
  private lastCaptureTime: number = 0;
  private processingTimes: number[] = [];

  /**
   * Creates a new FrameCaptureService instance.
   * 
   * Initializes the service with default or provided configuration settings.
   * Creates the internal canvas element used for frame processing and sets up
   * the initial statistics tracking state.
   * 
   * @param config - Partial configuration object to override defaults
   * @param config.width - Frame width in pixels (default: 640)
   * @param config.height - Frame height in pixels (default: 480)
   * @param config.quality - Image quality from 0.1 to 1.0 (default: 0.8)
   * @param config.format - Image format (default: 'image/jpeg')
   * @param config.captureRate - Capture rate in FPS (default: 10)
   * 
   * @throws {Error} When unable to create 2D canvas context
   * 
   * @example
   * ```typescript
   * // Create with default settings
   * const service = new FrameCaptureService();
   * 
   * // Create with custom configuration
   * const service = new FrameCaptureService({
   *   width: 1920,
   *   height: 1080,
   *   quality: 1.0,
   *   format: 'image/png',
   *   captureRate: 30
   * });
   * ```
   */
  constructor(config: Partial<FrameCaptureConfig> = {}) {
    this.config = {
      width: 640,
      height: 480,
      quality: 0.8,
      format: 'image/jpeg',
      captureRate: 10, // 10 FPS
      ...config
    };

    this.canvas = document.createElement('canvas');
    this.canvas.width = this.config.width;
    this.canvas.height = this.config.height;
    
    const context = this.canvas.getContext('2d');
    if (!context) {
      throw new Error('Cannot create 2D canvas context');
    }
    this.ctx = context;

    this.stats = {
      framesProcessed: 0,
      averageProcessingTime: 0,
      lastFrameTime: 0,
      isActive: false,
      matchesFound: 0
    };
  }

  /**
   * Sets the HTML video element to capture frames from.
   * 
   * This method must be called before starting any capture operations.
   * The video element should be playing and have valid video content.
   * 
   * @param video - The HTML video element to capture frames from
   * 
   * @example
   * ```typescript
   * const videoElement = document.querySelector('video') as HTMLVideoElement;
   * captureService.setVideoElement(videoElement);
   * ```
   */
  setVideoElement(video: HTMLVideoElement): void {
    this.video = video;
  }

  /**
   * Updates the capture configuration with new settings.
   * 
   * This method allows runtime modification of capture parameters.
   * Canvas dimensions are automatically updated when width or height changes.
   * Changes take effect immediately for new capture operations.
   * 
   * @param config - Partial configuration object with settings to update
   * @param config.width - New frame width in pixels
   * @param config.height - New frame height in pixels
   * @param config.quality - New image quality from 0.1 to 1.0
   * @param config.format - New image format
   * @param config.captureRate - New capture rate in FPS
   * 
   * @example
   * ```typescript
   * // Update capture quality and dimensions
   * captureService.updateConfig({
   *   width: 1280,
   *   height: 720,
   *   quality: 0.95
   * });
   * 
   * // Change capture rate for slower processing
   * captureService.updateConfig({
   *   captureRate: 5
   * });
   * ```
   */
  updateConfig(config: Partial<FrameCaptureConfig>): void {
    this.config = { ...this.config, ...config };
    this.canvas.width = this.config.width;
    this.canvas.height = this.config.height;
  }

  /**
   * Registers a callback function to be called when frames are captured.
   * 
   * The callback receives detailed information about each captured frame,
   * including the image data and capture metadata. This is useful for
   * processing captured frames or updating UI components.
   * 
   * @param callback - Function to call when a frame is captured
   * @param callback.result - The capture result containing frame data
   * @param callback.result.blob - Binary image data as a Blob
   * @param callback.result.dataUrl - Base64-encoded data URL of the image
   * @param callback.result.timestamp - Unix timestamp of capture
   * @param callback.result.frameNumber - Sequential frame number
   * 
   * @example
   * ```typescript
   * captureService.onFrame((result) => {
   *   console.log(`Frame ${result.frameNumber} captured at ${result.timestamp}`);
   *   
   *   // Display the frame in an image element
   *   const img = document.createElement('img');
   *   img.src = result.dataUrl;
   *   document.body.appendChild(img);
   * });
   * ```
   */
  onFrame(callback: (result: CaptureResult) => void): void {
    this.onFrameCallback = callback;
  }

  /**
   * Registers a callback function to be called when frame matching completes.
   * 
   * The callback receives match results from the API, including whether a match
   * was found and details about the matched asset. This is only called during
   * automatic matching operations.
   * 
   * @param callback - Function to call when frame matching completes
   * @param callback.result - The match result containing API response and capture info
   * @param callback.result.result - API response with match status and asset data
   * @param callback.result.captureInfo - Additional capture metadata
   * 
   * @example
   * ```typescript
   * captureService.onMatch((result) => {
   *   if (result.result.matched) {
   *     console.log('Match found!', result.result.asset);
   *     console.log(`Processing took ${result.captureInfo.processingTime}ms`);
   *   } else {
   *     console.log('No match found for frame', result.captureInfo.frameNumber);
   *   }
   * });
   * ```
   */
  onMatch(callback: (result: MatchResult) => void): void {
    this.onMatchCallback = callback;
  }

  /**
   * Registers a callback function to be called when errors occur.
   * 
   * The callback receives Error objects when issues arise during capture
   * or processing operations. This allows for custom error handling and
   * user notification.
   * 
   * @param callback - Function to call when an error occurs
   * @param callback.error - The Error object describing what went wrong
   * 
   * @example
   * ```typescript
   * captureService.onError((error) => {
   *   console.error('Capture error:', error.message);
   *   
   *   // Show user-friendly error message
   *   if (error.message.includes('Video element not set')) {
   *     showNotification('Please select a video source first');
   *   } else {
   *     showNotification('Frame capture failed. Please try again.');
   *   }
   * });
   * ```
   */
  onError(callback: (error: Error) => void): void {
    this.onErrorCallback = callback;
  }

  /**
   * Starts continuous frame capture from the video element.
   * 
   * Begins capturing frames at the configured frame rate. Each captured frame
   * triggers the onFrame callback if one is registered. The capture process
   * continues until stopCapture() is called.
   * 
   * @throws {Error} When video element is not set or capture fails
   * 
   * @example
   * ```typescript
   * // Set up capture service
   * captureService.setVideoElement(videoElement);
   * captureService.onFrame((result) => {
   *   console.log('Captured frame:', result.frameNumber);
   * });
   * 
   * // Start capturing at configured rate
   * captureService.startCapture();
   * 
   * // Stop after 10 seconds
   * setTimeout(() => {
   *   captureService.stopCapture();
   * }, 10000);
   * ```
   */
  startCapture(): void {
    if (!this.video) {
      this.handleError(new Error('Video element not set'));
      return;
    }

    if (this.isCapturing) {
      return;
    }

    this.isCapturing = true;
    this.stats.isActive = true;
    this.frameNumber = 0;
    
    const intervalMs = 1000 / this.config.captureRate;
    
    this.captureInterval = window.setInterval(() => {
      this.captureFrame();
    }, intervalMs);
  }

  /**
   * Stops the continuous frame capture process.
   * 
   * Halts the capture interval and updates the service state. Any ongoing
   * capture operations will complete, but no new captures will be started.
   * The service can be restarted with startCapture().
   * 
   * @example
   * ```typescript
   * // Stop capturing frames
   * captureService.stopCapture();
   * 
   * // Check if service is still active
   * const stats = captureService.getStats();
   * console.log('Capture active:', stats.isActive); // false
   * ```
   */
  stopCapture(): void {
    this.isCapturing = false;
    this.stats.isActive = false;
    
    if (this.captureInterval) {
      clearInterval(this.captureInterval);
      this.captureInterval = null;
    }
  }

  /**
   * Internal method to capture a single frame from the video element.
   * 
   * This private method handles the core frame capture logic:
   * - Draws the current video frame to the internal canvas
   * - Converts the canvas content to a blob and data URL
   * - Updates capture statistics and frame counters
   * - Triggers the onFrame callback with the result
   * 
   * This method is called automatically by startCapture() at the configured
   * frame rate and should not be called directly.
   * 
   * @private
   */
  private captureFrame(): void {
    if (!this.video || !this.isCapturing) {
      return;
    }

    const startTime = performance.now();

    try {
      // Draw video frame to canvas
      this.ctx.drawImage(
        this.video,
        0, 0,
        this.video.videoWidth, this.video.videoHeight,
        0, 0,
        this.config.width, this.config.height
      );

      // Convert canvas to blob
      this.canvas.toBlob(
        (blob) => {
          if (!blob) {
            this.handleError(new Error('Failed to create blob from canvas'));
            return;
          }

          const dataUrl = this.canvas.toDataURL(this.config.format, this.config.quality);
          const timestamp = Date.now();
          
          const result: CaptureResult = {
            blob,
            dataUrl,
            timestamp,
            frameNumber: this.frameNumber++
          };

          // Update stats
          const processingTime = performance.now() - startTime;
          this.updateStats(timestamp, processingTime);

          // Call frame callback
          if (this.onFrameCallback) {
            this.onFrameCallback(result);
          }

        },
        this.config.format,
        this.config.quality
      );

    } catch (error) {
      this.handleError(error as Error);
    }
  }

  /**
   * Captures a single frame and attempts to match it against campaign assets.
   * 
   * This method performs a one-time capture and match operation:
   * - Captures the current video frame
   * - Sends it to the API for matching against the specified campaign
   * - Returns detailed results including match status and processing metrics
   * - Updates match statistics if a match is found
   * 
   * @param campaignId - The ID of the campaign to match against
   * @returns Promise resolving to match result or null if capture is stopped
   * 
   * @throws {Error} When video element is not set or capture fails
   * 
   * @example
   * ```typescript
   * // Perform a single frame match
   * try {
   *   const result = await captureService.captureAndMatch('campaign-123');
   *   
   *   if (result && result.result.matched) {
   *     console.log('Match found!', result.result.asset);
   *     console.log(`Processing took ${result.captureInfo.processingTime}ms`);
   *   } else {
   *     console.log('No match found');
   *   }
   * } catch (error) {
   *   console.error('Match failed:', error);
   * }
   * ```
   */
  async captureAndMatch(campaignId: string): Promise<MatchResult | null> {
    if (!this.video) {
      throw new Error('Video element not set');
    }

    // Check if we're still capturing before starting the work
    if (!this.isCapturing) {
      return null;
    }

    const startTime = performance.now();

    try {
      // Capture current frame
      this.ctx.drawImage(
        this.video,
        0, 0,
        this.video.videoWidth, this.video.videoHeight,
        0, 0,
        this.config.width, this.config.height
      );

      // Convert to blob
      const blob = await new Promise<Blob>((resolve, reject) => {
        this.canvas.toBlob(
          (result) => {
            if (result) {
              resolve(result);
            } else {
              reject(new Error('Failed to create blob'));
            }
          },
          this.config.format,
          this.config.quality
        );
      });

      // Check if we're still capturing before making the API call
      if (!this.isCapturing) {
        return null;
      }

      // Match frame with API
      const matchResponse = await apiService.matchFrame(campaignId, blob);
      
      const processingTime = performance.now() - startTime;
      const timestamp = Date.now();

      if (matchResponse.result.matched) {
        this.stats.matchesFound++;
      }

      const result: MatchResult = {
        ...matchResponse,
        captureInfo: {
          timestamp,
          frameNumber: this.frameNumber++,
          processingTime
        }
      };

      // Update stats
      this.updateStats(timestamp, processingTime);

      // Call match callback only if we're still capturing
      if (this.onMatchCallback && this.isCapturing) {
        this.onMatchCallback(result);
      }

      return result;

    } catch (error) {
      this.handleError(error as Error);
      return null;
    }
  }

  /**
   * Starts continuous automatic frame capture and matching.
   * 
   * This method combines frame capture with automatic API matching:
   * - Captures frames at the configured rate
   * - Automatically sends each frame for matching against the campaign
   * - Triggers onMatch callbacks for each result
   * - Continues until stopCapture() is called
   * - Handles API errors gracefully without stopping capture
   * 
   * @param campaignId - The ID of the campaign to match frames against
   * @returns Promise that resolves when auto-matching is started
   * 
   * @throws {Error} When video element is not set
   * 
   * @example
   * ```typescript
   * // Set up automatic matching with callbacks
   * captureService.onMatch((result) => {
   *   if (result.result.matched) {
   *     console.log('Auto-match found:', result.result.asset);
   *     // Trigger AR overlay or other response
   *     showAROverlay(result.result.asset);
   *   }
   * });
   * 
   * // Start auto-matching for a specific campaign
   * await captureService.startAutoMatching('campaign-123');
   * 
   * // Stop after user interaction
   * document.addEventListener('click', () => {
   *   captureService.stopCapture();
   * });
   * ```
   */
  async startAutoMatching(campaignId: string): Promise<void> {
    if (!this.video) {
      this.handleError(new Error('Video element not set'));
      return;
    }

    if (this.isCapturing) {
      return;
    }

    this.isCapturing = true;
    this.stats.isActive = true;
    this.frameNumber = 0;
    
    const intervalMs = 1000 / this.config.captureRate;
    
    this.captureInterval = window.setInterval(async () => {
      // Double check we're still capturing before doing work
      if (!this.isCapturing) {
        return;
      }
      
      try {
        await this.captureAndMatch(campaignId);
      } catch (error) {
        this.handleError(error as Error);
      }
    }, intervalMs);
  }

  /**
   * Gets a snapshot of current capture statistics.
   * 
   * Returns a copy of the current statistics object containing performance
   * metrics and capture session information. The returned object is a copy
   * to prevent external modification of internal state.
   * 
   * @returns Copy of current capture statistics
   * 
   * @example
   * ```typescript
   * // Monitor capture performance
   * const stats = captureService.getStats();
   * console.log('Frames processed:', stats.framesProcessed);
   * console.log('Average processing time:', stats.averageProcessingTime + 'ms');
   * console.log('Matches found:', stats.matchesFound);
   * console.log('Currently active:', stats.isActive);
   * 
   * // Calculate match success rate
   * const successRate = stats.matchesFound / stats.framesProcessed * 100;
   * console.log(`Match success rate: ${successRate.toFixed(2)}%`);
   * ```
   */
  getStats(): StreamStats {
    return { ...this.stats };
  }

  /**
   * Resets all capture statistics to initial values.
   * 
   * Clears frame counters, processing times, and match counts while
   * preserving the current active state. This is useful for starting
   * fresh measurement periods or clearing data between sessions.
   * 
   * @example
   * ```typescript
   * // Reset stats before starting a new capture session
   * captureService.resetStats();
   * captureService.startAutoMatching('new-campaign-id');
   * 
   * // Later check fresh statistics
   * const stats = captureService.getStats();
   * console.log('New session frames:', stats.framesProcessed);
   * ```
   */
  resetStats(): void {
    this.stats = {
      framesProcessed: 0,
      averageProcessingTime: 0,
      lastFrameTime: 0,
      isActive: this.isCapturing,
      matchesFound: 0
    };
    this.processingTimes = [];
  }

  /**
   * Internal method to update capture statistics.
   * 
   * Updates frame counters, processing time averages, and timestamps.
   * Maintains a rolling window of the last 100 processing times for
   * accurate average calculation without unbounded memory growth.
   * 
   * @param timestamp - Unix timestamp of the processed frame
   * @param processingTime - Time taken to process the frame in milliseconds
   * 
   * @private
   */
  private updateStats(timestamp: number, processingTime: number): void {
    this.stats.framesProcessed++;
    this.stats.lastFrameTime = timestamp;
    
    // Keep last 100 processing times for average calculation
    this.processingTimes.push(processingTime);
    if (this.processingTimes.length > 100) {
      this.processingTimes.shift();
    }
    
    this.stats.averageProcessingTime = 
      this.processingTimes.reduce((sum, time) => sum + time, 0) / this.processingTimes.length;
  }

  /**
   * Internal method to handle and report errors.
   * 
   * Logs errors to the console and triggers the onError callback if
   * one is registered. This provides consistent error handling across
   * all service operations.
   * 
   * @param error - The Error object to handle
   * 
   * @private
   */
  private handleError(error: Error): void {
    console.error('FrameCaptureService error:', error);
    if (this.onErrorCallback) {
      this.onErrorCallback(error);
    }
  }

  /**
   * Static utility method to resize a canvas to new dimensions.
   * 
   * Creates a new canvas with the specified dimensions and draws the source
   * canvas content scaled to fit. This is useful for creating different
   * sized versions of captured frames for various purposes.
   * 
   * @param sourceCanvas - The canvas element to resize
   * @param targetWidth - Width of the new canvas in pixels
   * @param targetHeight - Height of the new canvas in pixels
   * @returns New canvas element with resized content
   * 
   * @throws {Error} When unable to create 2D canvas context
   * 
   * @example
   * ```typescript
   * // Create a thumbnail version of a captured frame
   * const thumbnailCanvas = FrameCaptureService.resizeFrame(
   *   originalCanvas,
   *   150,
   *   100
   * );
   * 
   * // Use the resized canvas
   * document.body.appendChild(thumbnailCanvas);
   * ```
   */
  static resizeFrame(
    sourceCanvas: HTMLCanvasElement,
    targetWidth: number,
    targetHeight: number
  ): HTMLCanvasElement {
    const resizedCanvas = document.createElement('canvas');
    resizedCanvas.width = targetWidth;
    resizedCanvas.height = targetHeight;
    
    const ctx = resizedCanvas.getContext('2d');
    if (!ctx) {
      throw new Error('Cannot create 2D canvas context');
    }
    
    ctx.drawImage(sourceCanvas, 0, 0, targetWidth, targetHeight);
    return resizedCanvas;
  }

  /**
   * Static utility method to convert a Blob to a data URL string.
   * 
   * Converts binary image data to a base64-encoded data URL that can be
   * used directly in image src attributes or for display purposes.
   * This is an asynchronous operation using FileReader.
   * 
   * @param blob - The Blob object to convert
   * @returns Promise resolving to base64 data URL string
   * 
   * @example
   * ```typescript
   * // Convert capture result blob to displayable URL
   * const dataUrl = await FrameCaptureService.blobToDataUrl(result.blob);
   * 
   * // Display in an image element
   * const img = document.createElement('img');
   * img.src = dataUrl;
   * document.body.appendChild(img);
   * ```
   */
  static async blobToDataUrl(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  /**
   * Static utility method to convert a data URL string to a Blob.
   * 
   * Converts a base64-encoded data URL back to binary Blob format.
   * This is useful for sending data URL content to APIs that expect
   * binary data or for file operations.
   * 
   * @param dataUrl - The data URL string to convert (format: data:mime/type;base64,data)
   * @returns Blob object containing the decoded binary data
   * 
   * @example
   * ```typescript
   * // Convert data URL back to blob for API upload
   * const dataUrl = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEA...';
   * const blob = FrameCaptureService.dataUrlToBlob(dataUrl);
   * 
   * // Send to API
   * await apiService.uploadImage(blob);
   * ```
   */
  static dataUrlToBlob(dataUrl: string): Blob {
    const arr = dataUrl.split(',');
    const mimeMatch = arr[0].match(/:(.*?);/);
    const mime = mimeMatch ? mimeMatch[1] : 'image/jpeg';
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    
    return new Blob([u8arr], { type: mime });
  }

  /**
   * Destroys the service instance and cleans up resources.
   * 
   * Stops any active capture operations, clears all callbacks, and
   * removes references to external objects. This method should be called
   * when the service is no longer needed to prevent memory leaks.
   * 
   * After calling destroy(), the service instance should not be used again.
   * 
   * @example
   * ```typescript
   * // Clean up when component unmounts
   * useEffect(() => {
   *   const captureService = new FrameCaptureService();
   *   
   *   return () => {
   *     // Clean up resources
   *     captureService.destroy();
   *   };
   * }, []);
   * 
   * // Or manually when done
   * captureService.destroy();
   * ```
   */
  destroy(): void {
    this.stopCapture();
    this.video = null;
    this.onFrameCallback = undefined;
    this.onMatchCallback = undefined;
    this.onErrorCallback = undefined;
  }
}