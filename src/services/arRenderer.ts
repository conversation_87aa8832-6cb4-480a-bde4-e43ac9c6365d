/**
 * @fileoverview AR Renderer service providing 3D model rendering and pose-based augmented reality.
 * Built on Three.js for WebGL rendering with camera calibration and model management.
 *
 * <AUTHOR> AR System
 * @version 1.0.0
 */

import * as THREE from "three";
import { Pose3D, CameraIntrinsics, Vector3D } from "../types/pose";

/** Available 3D model types for AR rendering */
export type ModelType = "cube" | "sphere" | "cone" | "littletokyo" | "tictactoe";

/**
 * Utility functions for AR rendering and mathematical operations.
 * Provides common transformations and camera calibration helpers.
 *
 * @public
 */
export const ARUtils = {
  /**
   * Converts degrees to radians.
   *
   * @param degrees - Angle in degrees
   * @returns Angle in radians
   */
  degToRad: (degrees: number): number => degrees * (Math.PI / 180),

  /**
   * Converts radians to degrees.
   *
   * @param radians - Angle in radians
   * @returns Angle in degrees
   */
  radToDeg: (radians: number): number => radians * (180 / Math.PI),

  /**
   * Creates default camera intrinsic parameters for a given image size.
   * Uses reasonable defaults when calibration data is not available.
   *
   * @param width - Image width in pixels
   * @param height - Image height in pixels
   * @returns Default camera intrinsics
   */
  createDefaultIntrinsics: (
    width: number,
    height: number,
  ): CameraIntrinsics => ({
    focal_length: { x: width * 0.9, y: height * 0.9 },
    principal_point: { x: width / 2, y: height / 2 },
    distortion_coefficients: [0, 0, 0, 0, 0],
    image_size: { x: width, y: height },
  }),

  /**
   * Returns all available 3D model types supported by the AR system.
   *
   * @returns Array of supported model types
   */
  getAvailableModelTypes: (): ModelType[] => [
    "cube",
    "sphere",
    "cone",
    "littletokyo",
  ],
};

// 3D Model interfaces
export interface ARModel {
  id: string;
  type: ModelType;
  mesh: THREE.Mesh;
  position: Vector3D;
  rotation: Vector3D;
  scale: Vector3D;
  visible: boolean;
  opacity: number;
  color: string;
  animation?: ARModelAnimation;
}

export interface ARModelConfig {
  type: ModelType;
  position?: Vector3D;
  rotation?: Vector3D;
  scale?: Vector3D;
  color?: string;
  opacity?: number;
  wireframe?: boolean;
  castShadow?: boolean;
  receiveShadow?: boolean;
}

export interface ARModelAnimation {
  type: "rotation" | "scale" | "position" | "custom";
  duration: number;
  loop: boolean;
  easing: "linear" | "easeIn" | "easeOut" | "easeInOut";
  keyframes: ARAnimationKeyframe[];
}

export interface ARAnimationKeyframe {
  time: number; // 0-1
  position?: Vector3D;
  rotation?: Vector3D;
  scale?: Vector3D;
  opacity?: number;
}

// Camera tracking interfaces
export interface ARCamera {
  fov: number;
  aspect: number;
  near: number;
  far: number;
  position: Vector3D;
  rotation: Vector3D;
  projectionMatrix: THREE.Matrix4;
  viewMatrix: THREE.Matrix4;
}

export interface CameraTrackingConfig {
  smoothing: number; // 0-1, higher = more smoothing
  enablePrediction: boolean;
  predictionTime: number; // ms
  stabilizationThreshold: number;
  maxTrackingDistance: number;
}

// Scene configuration
export interface ARSceneConfig {
  enableShadows: boolean;
  shadowMapSize: number;
  ambientLightIntensity: number;
  directionalLightIntensity: number;
  backgroundColor: string;
  fog?: {
    color: string;
    near: number;
    far: number;
  };
}

// Rendering statistics
export interface RenderStats {
  fps: number;
  frameTime: number;
  triangles: number;
  drawCalls: number;
  memoryUsage: {
    geometries: number;
    textures: number;
    materials: number;
  };
}

/**
 * Main AR rendering engine built on Three.js.
 * Manages 3D scene, camera calibration, model rendering, and pose tracking.
 * Provides real-time augmented reality visualization capabilities.
 *
 * @public
 */
export class ARRenderer {
  /** Three.js scene containing all 3D objects */
  private scene: THREE.Scene;
  /** Three.js perspective camera with AR calibration */
  private camera: THREE.PerspectiveCamera;
  /** Three.js WebGL renderer for hardware-accelerated rendering */
  private renderer: THREE.WebGLRenderer;
  /** Map of all managed AR models */
  private models: Map<string, ARModel>;
  /** Three.js animation mixer for model animations */
  private animationMixer: THREE.AnimationMixer;
  /** Three.js clock for animation timing */
  private clock: THREE.Clock;
  /** Whether the renderer has been fully initialized */
  private isInitialized: boolean = false;
  /** Whether the render loop is currently active */
  private isRendering: boolean = false;
  /** Animation frame request ID for the render loop */
  private renderRequestId: number | null = null;

  // Tracking and calibration
  private cameraIntrinsics: CameraIntrinsics | null = null;
  private currentPose: Pose3D | null = null;
  private trackingConfig: CameraTrackingConfig;
  private poseHistory: Pose3D[] = [];
  private lastUpdateTime: number = 0;
  private targetPose: Pose3D | null = null;
  private interpolationFactor: number = 0;

  // Scene configuration
  private sceneConfig: ARSceneConfig;

  // Performance monitoring
  private stats: RenderStats = {
    fps: 0,
    frameTime: 0,
    triangles: 0,
    drawCalls: 0,
    memoryUsage: { geometries: 0, textures: 0, materials: 0 },
  };
  private frameCount = 0;
  private lastStatsUpdate = 0;

  /**
   * Creates a new AR renderer instance.
   * Initializes Three.js components, sets up scene lighting, and configures render settings.
   *
   * @param canvas - HTML canvas element for WebGL rendering
   * @param width - Canvas width in pixels
   * @param height - Canvas height in pixels
   * @param config - Optional scene and tracking configuration
   * @throws Error when WebGL is not supported or canvas is invalid
   */
  constructor(
    canvas: HTMLCanvasElement,
    width: number = 800,
    height: number = 600,
    config?: Partial<ARSceneConfig & CameraTrackingConfig>,
  ) {
    try {
      // Initialize Three.js components
      this.scene = new THREE.Scene();
      this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
      this.renderer = new THREE.WebGLRenderer({
        canvas,
        alpha: true,
        antialias: true,
        precision: "highp",
        powerPreference: "high-performance",
      });
    } catch (error) {
      throw error;
    }

    this.models = new Map();
    this.animationMixer = new THREE.AnimationMixer(this.scene);
    this.clock = new THREE.Clock();

    // Default configurations
    this.sceneConfig = {
      enableShadows: true,
      shadowMapSize: 1024,
      ambientLightIntensity: 0.6,
      directionalLightIntensity: 0.8,
      backgroundColor: "transparent",
      ...config,
    };

    this.trackingConfig = {
      smoothing: 0.85, // Balanced smoothing for stability without lag
      enablePrediction: true,
      predictionTime: 8, // Ultra high frequency prediction
      stabilizationThreshold: 0.002, // Very tight threshold for smooth motion
      maxTrackingDistance: 10.0,
      ...config,
    };

    this.setupRenderer(width, height);
    this.setupScene();
  }

  /**
   * Initializes the Three.js WebGL renderer with optimized settings for AR applications.
   * Configures pixel ratio, shadow mapping, tone mapping, and color space for high-quality rendering.
   * Sets up hardware-accelerated rendering with proper transparency support.
   *
   * @param width - Canvas width in pixels for renderer viewport
   * @param height - Canvas height in pixels for renderer viewport
   * @throws Error when WebGL context creation fails or hardware acceleration unavailable
   *
   * @private
   */
  private setupRenderer(width: number, height: number): void {
    this.renderer.setSize(width, height);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    this.renderer.setClearColor(0x000000, 0); // Transparent background

    if (this.sceneConfig.enableShadows) {
      this.renderer.shadowMap.enabled = true;
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
      this.renderer.shadowMap.autoUpdate = true;
    }

    // Enable tone mapping for better colors
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
    this.renderer.toneMappingExposure = 1.0;

    // Output encoding
    this.renderer.outputColorSpace = THREE.SRGBColorSpace;
  }

  /**
   * Sets up the Three.js scene with proper lighting and environmental settings for AR rendering.
   * Creates ambient and directional lighting with configurable shadow mapping.
   * Applies fog effects when configured and marks the renderer as fully initialized.
   *
   * @throws Error when lighting setup fails or shadow mapping configuration is invalid
   *
   * @remarks
   * - Ambient light provides overall scene illumination
   * - Directional light acts as sun/primary light source with shadows
   * - Shadow mapping uses PCF soft shadows for realistic lighting
   * - Fog effects are optional based on scene configuration
   *
   * @private
   */
  private setupScene(): void {
    // Ambient light
    const ambientLight = new THREE.AmbientLight(
      0xffffff,
      this.sceneConfig.ambientLightIntensity,
    );
    this.scene.add(ambientLight);

    // Directional light (sun)
    const directionalLight = new THREE.DirectionalLight(
      0xffffff,
      this.sceneConfig.directionalLightIntensity,
    );
    directionalLight.position.set(5, 10, 5);
    directionalLight.castShadow = this.sceneConfig.enableShadows;

    if (this.sceneConfig.enableShadows) {
      directionalLight.shadow.mapSize.width = this.sceneConfig.shadowMapSize;
      directionalLight.shadow.mapSize.height = this.sceneConfig.shadowMapSize;
      directionalLight.shadow.camera.near = 0.5;
      directionalLight.shadow.camera.far = 50;
      directionalLight.shadow.camera.left = -10;
      directionalLight.shadow.camera.right = 10;
      directionalLight.shadow.camera.top = 10;
      directionalLight.shadow.camera.bottom = -10;
    }

    this.scene.add(directionalLight);

    // Add fog if configured
    if (this.sceneConfig.fog) {
      this.scene.fog = new THREE.Fog(
        this.sceneConfig.fog.color,
        this.sceneConfig.fog.near,
        this.sceneConfig.fog.far,
      );
    }

    this.isInitialized = true;
  }

  /**
   * Sets camera intrinsic parameters for accurate AR projection and calibration.
   * Updates the Three.js camera projection matrix based on real camera calibration data.
   * Essential for proper 3D object placement and scale in AR applications.
   *
   * @param intrinsics - Calibrated camera parameters from camera calibration process
   * @param intrinsics.focal_length - Camera focal length in pixels {x, y}
   * @param intrinsics.principal_point - Principal point coordinates in pixels {x, y}
   * @param intrinsics.distortion_coefficients - Lens distortion coefficients array
   * @param intrinsics.image_size - Image dimensions {x: width, y: height}
   *
   * @example
   * ```typescript
   * const arRenderer = new ARRenderer(canvas, 640, 480);
   * const intrinsics = {
   *   focal_length: { x: 525.0, y: 525.0 },
   *   principal_point: { x: 320.0, y: 240.0 },
   *   distortion_coefficients: [0.1, -0.2, 0.01, 0.005, 0],
   *   image_size: { x: 640, y: 480 }
   * };
   * arRenderer.setCameraIntrinsics(intrinsics);
   * ```
   *
   * @public
   */
  setCameraIntrinsics(intrinsics: CameraIntrinsics): void {
    this.cameraIntrinsics = intrinsics;
    this.updateCameraProjection();
  }

  /**
   * Updates the Three.js camera projection matrix using stored camera intrinsics.
   * Converts camera calibration parameters to OpenGL-compatible projection matrix.
   * Ensures pixel-perfect alignment between virtual objects and real camera view.
   *
   * @throws Error when camera intrinsics are not set or contain invalid values
   *
   * @remarks
   * The projection matrix transformation converts from camera coordinates to NDC:
   * - Maps focal lengths to field-of-view parameters
   * - Adjusts for different coordinate systems (OpenCV vs OpenGL)
   * - Maintains proper aspect ratio and depth range
   *
   * @private
   */
  private updateCameraProjection(): void {
    if (!this.cameraIntrinsics) return;

    const { focal_length, principal_point, image_size } = this.cameraIntrinsics;

    // Convert camera intrinsics to Three.js projection matrix
    const near = this.camera.near;
    const far = this.camera.far;
    const width = image_size.x;
    const height = image_size.y;

    // Create projection matrix from camera intrinsics
    const projMatrix = new THREE.Matrix4();
    projMatrix.set(
      (2 * focal_length.x) / width,
      0,
      2 * (principal_point.x / width) - 1,
      0,
      0,
      (2 * focal_length.y) / height,
      2 * (principal_point.y / height) - 1,
      0,
      0,
      0,
      -(far + near) / (far - near),
      (-2 * far * near) / (far - near),
      0,
      0,
      -1,
      0,
    );

    this.camera.projectionMatrix = projMatrix;
    this.camera.projectionMatrixInverse = projMatrix.clone().invert();
  }

  /**
   * Updates camera pose and model positioning from 6DOF tracking data.
   * Applies pose smoothing and updates all visible AR models for consistent visualization.
   * Maintains pose history for temporal filtering and prediction capabilities.
   *
   * @param pose - 6DOF pose data from pose estimation service
   * @param pose.translation - Camera position in 3D space (meters)
   * @param pose.rotation - Camera orientation as quaternion {x, y, z, w}
   * @param pose.scale - Scale factors (typically {1, 1, 1} for rigid cameras)
   * @param pose.confidence - Pose estimation confidence score (0-1)
   *
   * @example
   * ```typescript
   * const arRenderer = new ARRenderer(canvas, 640, 480);
   *
   * // Update from pose estimation service
   * const pose = {
   *   translation: { x: 0.1, y: 0.05, z: -0.5 },
   *   rotation: { x: 0, y: 0, z: 0, w: 1 },
   *   scale: { x: 1, y: 1, z: 1 },
   *   confidence: 0.85
   * };
   * arRenderer.updateCameraPose(pose);
   * ```
   *
   * @remarks
   * - Pose history is maintained for smoothing (last 10 poses)
   * - Smoothing reduces jitter but adds slight latency
   * - Models are positioned as screen overlay for current implementation
   * - Animation effects are applied to models for visual appeal
   *
   * @public
   */
  updateCameraPose(pose: Pose3D): void {
    if (!this.isInitialized) return;

    // Store pose in history for smoothing
    this.poseHistory.push(pose);
    if (this.poseHistory.length > 10) {
      this.poseHistory.shift();
    }

    // Apply smoothing if enabled
    const smoothedPose =
      this.trackingConfig.smoothing > 0 ? this.smoothPose(pose) : pose;

    // Set target pose for interpolation in render loop
    this.targetPose = smoothedPose;
    this.lastUpdateTime = performance.now();

    // Initialize current pose if not set
    if (!this.currentPose) {
      this.currentPose = smoothedPose;
    }

    // Position models as screen overlay (not tracking specific object position)
    this.models.forEach((model) => {
      if (model.visible) {
        // Simple fixed overlay position and scale for screen overlay
        model.mesh.position.set(0, 0, -3);
        model.mesh.scale.set(2, 2, 2); // Fixed scale for visibility

        // Add gentle rotation animation for visual appeal
        model.mesh.rotation.y += 0.01; // Slow Y-axis rotation
        model.mesh.rotation.x += 0.005; // Even slower X-axis rotation
      }
    });
  }

  /**
   * Applies exponential moving average smoothing to reduce pose estimation jitter.
   * Smooths both translation and rotation components while preserving pose accuracy.
   * Uses SLERP (Spherical Linear Interpolation) for smooth quaternion transitions.
   *
   * @param newPose - New pose estimate to be smoothed
   * @returns Smoothed pose combining current and historical pose data
   *
   * @remarks
   * - Translation uses linear interpolation with configurable alpha
   * - Rotation uses quaternion SLERP for mathematically correct interpolation
   * - Smoothing factor (1 - config.smoothing) controls responsiveness vs stability
   * - Higher smoothing values reduce jitter but increase latency
   *
   * @private
   */
  private smoothPose(newPose: Pose3D): Pose3D {
    if (!this.currentPose || this.trackingConfig.smoothing === 0) {
      return newPose;
    }

    const alpha = 1 - this.trackingConfig.smoothing;

    // Smooth translation
    const translation: Vector3D = {
      x:
        this.currentPose.translation.x * (1 - alpha) +
        newPose.translation.x * alpha,
      y:
        this.currentPose.translation.y * (1 - alpha) +
        newPose.translation.y * alpha,
      z:
        this.currentPose.translation.z * (1 - alpha) +
        newPose.translation.z * alpha,
    };

    // Smooth rotation using quaternion SLERP
    const currentQuat = new THREE.Quaternion(
      this.currentPose.rotation.x,
      this.currentPose.rotation.y,
      this.currentPose.rotation.z,
      this.currentPose.rotation.w,
    );

    const newQuat = new THREE.Quaternion(
      newPose.rotation.x,
      newPose.rotation.y,
      newPose.rotation.z,
      newPose.rotation.w,
    );

    currentQuat.slerp(newQuat, alpha);

    return {
      translation,
      rotation: {
        x: currentQuat.x,
        y: currentQuat.y,
        z: currentQuat.z,
        w: currentQuat.w,
      },
      scale: newPose.scale,
      confidence: newPose.confidence,
    };
  }

  /**
   * Creates a new 3D model in the AR scene with specified geometry and materials.
   * Supports multiple primitive geometries and configures rendering properties.
   * Adds the model to both the Three.js scene and internal model management system.
   *
   * @param id - Unique identifier for the model (used for updates and removal)
   * @param config - Model configuration specifying geometry, materials, and transform
   * @param config.type - Geometry type: "cube", "sphere", "cone", or "littletokyo"
   * @param config.position - Initial 3D position {x, y, z} in world coordinates
   * @param config.rotation - Initial rotation {x, y, z} in radians
   * @param config.scale - Scale factors {x, y, z} for non-uniform scaling
   * @param config.color - Material color as CSS color string (default: "#ffffff")
   * @param config.opacity - Material transparency (0-1, default: 1.0)
   * @param config.wireframe - Whether to render as wireframe (default: false)
   * @param config.castShadow - Whether model casts shadows (default: true)
   * @param config.receiveShadow - Whether model receives shadows (default: true)
   * @returns ARModel object containing mesh and metadata for scene management
   * @throws Error when geometry creation fails or model ID already exists
   *
   * @example
   * ```typescript
   * const arRenderer = new ARRenderer(canvas, 640, 480);
   *
   * // Create a red cube at origin
   * const cube = arRenderer.createModel("myCube", {
   *   type: "cube",
   *   position: { x: 0, y: 0, z: -2 },
   *   color: "#ff0000",
   *   scale: { x: 0.5, y: 0.5, z: 0.5 }
   * });
   *
   * // Create a semi-transparent sphere
   * const sphere = arRenderer.createModel("mySphere", {
   *   type: "sphere",
   *   position: { x: 1, y: 0, z: -2 },
   *   color: "#00ff00",
   *   opacity: 0.7
   * });
   * ```
   *
   * @public
   */
  createModel(id: string, config: ARModelConfig): ARModel {
    let geometry: THREE.BufferGeometry;

    // Create geometry based on model type
    switch (config.type) {
      case "cube":
        geometry = new THREE.BoxGeometry(1, 1, 1);
        break;
      case "sphere":
        geometry = new THREE.SphereGeometry(0.5, 32, 32);
        break;
      case "cone":
        geometry = new THREE.ConeGeometry(0.5, 1, 32);
        break;
      case "littletokyo":
        // Create a complex geometry for littletokyo (using multiple primitives for now)
        geometry = new THREE.TorusKnotGeometry(0.5, 0.2, 100, 16);
        break;
      default:
        geometry = new THREE.BoxGeometry(1, 1, 1);
    }

    // Create material
    const material = new THREE.MeshPhongMaterial({
      color: config.color || "#ffffff",
      transparent: true,
      opacity: config.opacity || 1.0,
      wireframe: config.wireframe || false,
    });

    // Create mesh
    const mesh = new THREE.Mesh(geometry, material);

    // Configure shadows
    mesh.castShadow = config.castShadow !== false;
    mesh.receiveShadow = config.receiveShadow !== false;

    // Set initial transform
    if (config.position) {
      mesh.position.set(
        config.position.x,
        config.position.y,
        config.position.z,
      );
    }
    if (config.rotation) {
      mesh.rotation.set(
        config.rotation.x,
        config.rotation.y,
        config.rotation.z,
      );
    }
    if (config.scale) {
      mesh.scale.set(config.scale.x, config.scale.y, config.scale.z);
    }

    // Create AR model wrapper
    const arModel: ARModel = {
      id,
      type: config.type,
      mesh,
      position: config.position || { x: 0, y: 0, z: 0 },
      rotation: config.rotation || { x: 0, y: 0, z: 0 },
      scale: config.scale || { x: 1, y: 1, z: 1 },
      visible: true, // Will be set correctly after creation
      opacity: config.opacity || 1.0,
      color: config.color || "#ffffff",
    };

    // Add to scene and store
    this.scene.add(mesh);
    this.models.set(id, arModel);

    return arModel;
  }

  /**
   * Updates an existing AR model's properties including transform, visibility, and materials.
   * Allows partial updates to any model property without affecting unchanged attributes.
   * Immediately applies changes to the Three.js mesh for real-time updates.
   *
   * @param id - Unique identifier of the model to update
   * @param transform - Partial model properties to update
   * @param transform.position - New 3D position {x, y, z}
   * @param transform.rotation - New rotation {x, y, z} in radians
   * @param transform.scale - New scale factors {x, y, z}
   * @param transform.visible - Visibility state (true/false)
   * @param transform.opacity - Material transparency (0-1)
   * @param transform.color - Material color as CSS color string
   *
   * @example
   * ```typescript
   * const arRenderer = new ARRenderer(canvas, 640, 480);
   *
   * // Move model to new position
   * arRenderer.updateModel("myCube", {
   *   position: { x: 1, y: 0.5, z: -3 }
   * });
   *
   * // Change color and make semi-transparent
   * arRenderer.updateModel("myCube", {
   *   color: "#0000ff",
   *   opacity: 0.5
   * });
   *
   * // Hide model temporarily
   * arRenderer.updateModel("myCube", {
   *   visible: false
   * });
   * ```
   *
   * @remarks
   * - Only specified properties are updated; others remain unchanged
   * - Position, rotation, and scale directly update mesh transform
   * - Color and opacity changes require material property updates
   * - Visibility changes affect both model state and mesh rendering
   *
   * @public
   */
  updateModel(id: string, transform: Partial<ARModel>): void {
    const model = this.models.get(id);
    if (!model) return;

    if (transform.position) {
      model.position = transform.position;
      model.mesh.position.set(
        transform.position.x,
        transform.position.y,
        transform.position.z,
      );
    }

    if (transform.rotation) {
      model.rotation = transform.rotation;
      model.mesh.rotation.set(
        transform.rotation.x,
        transform.rotation.y,
        transform.rotation.z,
      );
    }

    if (transform.scale) {
      model.scale = transform.scale;
      model.mesh.scale.set(
        transform.scale.x,
        transform.scale.y,
        transform.scale.z,
      );
    }

    if (transform.visible !== undefined) {
      model.visible = transform.visible;
      model.mesh.visible = transform.visible;
    }

    if (transform.opacity !== undefined) {
      model.opacity = transform.opacity;
      (model.mesh.material as THREE.MeshPhongMaterial).opacity =
        transform.opacity;
    }

    if (transform.color) {
      model.color = transform.color;
      (model.mesh.material as THREE.MeshPhongMaterial).color.set(
        transform.color,
      );
    }
  }

  /**
   * Removes an AR model from the scene and cleans up associated resources.
   * Properly disposes of Three.js geometry and materials to prevent memory leaks.
   * Removes the model from both the Three.js scene and internal management system.
   *
   * @param id - Unique identifier of the model to remove
   *
   * @example
   * ```typescript
   * const arRenderer = new ARRenderer(canvas, 640, 480);
   *
   * // Create a model
   * arRenderer.createModel("tempCube", { type: "cube" });
   *
   * // Later, remove it
   * arRenderer.removeModel("tempCube");
   * ```
   *
   * @remarks
   * - Automatically disposes geometry and material to free GPU memory
   * - Safe to call with non-existent model IDs (silently ignored)
   * - Removed models cannot be recovered; use visibility instead for temporary hiding
   *
   * @public
   */
  removeModel(id: string): void {
    const model = this.models.get(id);
    if (!model) return;

    this.scene.remove(model.mesh);
    model.mesh.geometry.dispose();
    (model.mesh.material as THREE.Material).dispose();
    this.models.delete(id);
  }

  /**
   * Retrieves an AR model by its unique identifier.
   * Provides access to model properties and state for inspection or conditional logic.
   *
   * @param id - Unique identifier of the model to retrieve
   * @returns ARModel object if found, null if model does not exist
   *
   * @example
   * ```typescript
   * const arRenderer = new ARRenderer(canvas, 640, 480);
   *
   * // Check if model exists and get its properties
   * const model = arRenderer.getModel("myCube");
   * if (model) {
   *   console.log(`Model position: ${model.position.x}, ${model.position.y}, ${model.position.z}`);
   *   console.log(`Model visible: ${model.visible}`);
   * }
   * ```
   *
   * @public
   */
  getModel(id: string): ARModel | null {
    return this.models.get(id) || null;
  }

  /**
   * Retrieves all AR models currently managed by the renderer.
   * Useful for bulk operations, debugging, or scene inspection.
   *
   * @returns Array of all ARModel objects in the scene
   *
   * @example
   * ```typescript
   * const arRenderer = new ARRenderer(canvas, 640, 480);
   *
   * // Get all models and hide them
   * const allModels = arRenderer.getAllModels();
   * allModels.forEach(model => {
   *   arRenderer.updateModel(model.id, { visible: false });
   * });
   *
   * // Count visible models
   * const visibleCount = allModels.filter(model => model.visible).length;
   * console.log(`Visible models: ${visibleCount}`);
   * ```
   *
   * @public
   */
  getAllModels(): ARModel[] {
    return Array.from(this.models.values());
  }

  /**
   * Starts the main rendering loop for continuous AR visualization.
   * Uses requestAnimationFrame for smooth 60fps rendering synchronized with display refresh.
   * Safe to call multiple times; will not create duplicate render loops.
   *
   * @example
   * ```typescript
   * const arRenderer = new ARRenderer(canvas, 640, 480);
   * arRenderer.createModel("cube", { type: "cube" });
   *
   * // Start rendering
   * arRenderer.startRendering();
   *
   * // Rendering continues until stopRendering() is called
   * ```
   *
   * @remarks
   * - Automatically updates animations and model transforms each frame
   * - Updates performance statistics for monitoring
   * - Renders the complete 3D scene to the canvas
   * - Essential for real-time AR applications
   *
   * @public
   */
  startRendering(): void {
    if (this.isRendering) return;
    this.isRendering = true;
    this.renderLoop();
  }

  /**
   * Stops the rendering loop and cancels any pending animation frames.
   * Immediately halts all rendering operations to conserve CPU/GPU resources.
   * Can be restarted later by calling startRendering() again.
   *
   * @example
   * ```typescript
   * const arRenderer = new ARRenderer(canvas, 640, 480);
   * arRenderer.startRendering();
   *
   * // Later, stop rendering to save resources
   * arRenderer.stopRendering();
   *
   * // Can restart rendering when needed
   * arRenderer.startRendering();
   * ```
   *
   * @remarks
   * - Cancels the current animation frame request if pending
   * - Models remain in scene but are not visually updated
   * - Useful for pausing AR when not needed or during background operations
   *
   * @public
   */
  stopRendering(): void {
    this.isRendering = false;
    if (this.renderRequestId) {
      cancelAnimationFrame(this.renderRequestId);
      this.renderRequestId = null;
    }
  }

  /**
   * Main rendering loop that executes each animation frame.
   * Handles camera positioning, model animations, statistics updates, and scene rendering.
   * Implements screen overlay mode where models appear as UI elements over the camera feed.
   *
   * @remarks
   * - Camera remains stationary at origin looking forward (-Z direction)
   * - Models are positioned as screen overlays at fixed distance
   * - Applies gentle rotation animation to models for visual appeal
   * - Updates animation mixer for any complex model animations
   * - Collects performance statistics every frame
   * - Final render call draws the complete scene
   *
   * @private
   */
  private renderLoop = (): void => {
    if (!this.isRendering) return;

    this.renderRequestId = requestAnimationFrame(this.renderLoop);

    const deltaTime = this.clock.getDelta();

    // Keep camera stationary for screen overlay mode
    // Camera stays at origin looking forward, models appear as overlay
    this.camera.position.set(0, 0, 0);
    this.camera.lookAt(0, 0, -1);

    // Update model positions for overlay (do this every frame)
    this.models.forEach((model) => {
      if (model.visible) {
        // Position model as screen overlay
        model.mesh.position.set(0, 0, -3);
        model.mesh.scale.set(2, 2, 2);

        // Add gentle rotation animation
        model.mesh.rotation.y += 0.01;
        model.mesh.rotation.x += 0.005;
      }
    });

    // Update animations
    this.animationMixer.update(deltaTime);

    // Update statistics
    this.updateStats();

    // Render scene
    this.renderer.render(this.scene, this.camera);
  };

  /**
   * Updates rendering performance statistics for monitoring and optimization.
   * Calculates FPS, frame times, and memory usage metrics from Three.js renderer info.
   * Updates statistics every second to avoid performance overhead.
   *
   * @remarks
   * Statistics include:
   * - FPS: Frames per second over the last measurement period
   * - Frame time: Average time per frame in milliseconds
   * - Triangle count: Total triangles rendered per frame
   * - Draw calls: Number of GPU draw calls per frame
   * - Memory usage: Geometries, textures, and materials in GPU memory
   *
   * @private
   */
  private updateStats(): void {
    this.frameCount++;
    const now = performance.now();

    if (now - this.lastStatsUpdate > 1000) {
      // Update every second
      this.stats.fps = Math.round(
        (this.frameCount * 1000) / (now - this.lastStatsUpdate),
      );
      this.stats.frameTime = (now - this.lastStatsUpdate) / this.frameCount;

      // Get renderer info
      const info = this.renderer.info;
      this.stats.triangles = info.render.triangles;
      this.stats.drawCalls = info.render.calls;
      this.stats.memoryUsage = {
        geometries: info.memory.geometries,
        textures: info.memory.textures,
        materials: this.models.size,
      };

      this.frameCount = 0;
      this.lastStatsUpdate = now;
    }
  }

  /**
   * Resizes the renderer viewport and updates camera projection for new dimensions.
   * Should be called when canvas size changes to maintain proper aspect ratio.
   * Updates both renderer size and camera projection matrix.
   *
   * @param width - New canvas width in pixels
   * @param height - New canvas height in pixels
   *
   * @example
   * ```typescript
   * const arRenderer = new ARRenderer(canvas, 640, 480);
   *
   * // Handle window resize
   * window.addEventListener('resize', () => {
   *   const newWidth = window.innerWidth;
   *   const newHeight = window.innerHeight;
   *   canvas.width = newWidth;
   *   canvas.height = newHeight;
   *   arRenderer.resize(newWidth, newHeight);
   * });
   * ```
   *
   * @public
   */
  resize(width: number, height: number): void {
    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(width, height);
  }

  /**
   * Retrieves current rendering performance statistics.
   * Provides detailed metrics for performance monitoring and optimization.
   * Returns a copy of internal statistics to prevent external modification.
   *
   * @returns RenderStats object containing performance metrics
   * @returns RenderStats.fps - Current frames per second
   * @returns RenderStats.frameTime - Average frame time in milliseconds
   * @returns RenderStats.triangles - Triangle count per frame
   * @returns RenderStats.drawCalls - GPU draw calls per frame
   * @returns RenderStats.memoryUsage - Memory usage breakdown
   *
   * @example
   * ```typescript
   * const arRenderer = new ARRenderer(canvas, 640, 480);
   * arRenderer.startRendering();
   *
   * // Monitor performance
   * setInterval(() => {
   *   const stats = arRenderer.getStats();
   *   console.log(`FPS: ${stats.fps}, Triangles: ${stats.triangles}`);
   *   console.log(`GPU Memory - Geometries: ${stats.memoryUsage.geometries}`);
   * }, 1000);
   * ```
   *
   * @public
   */
  getStats(): RenderStats {
    return { ...this.stats };
  }

  /**
   * Retrieves the current camera pose after smoothing and processing.
   * Returns the last processed pose from pose estimation and tracking.
   *
   * @returns Current camera pose with position, rotation, and confidence, or null if no pose set
   *
   * @example
   * ```typescript
   * const arRenderer = new ARRenderer(canvas, 640, 480);
   *
   * // After updating pose from tracking
   * const currentPose = arRenderer.getCurrentPose();
   * if (currentPose) {
   *   console.log(`Camera position: x=${currentPose.translation.x}, y=${currentPose.translation.y}, z=${currentPose.translation.z}`);
   *   console.log(`Pose confidence: ${currentPose.confidence}`);
   * }
   * ```
   *
   * @public
   */
  getCurrentPose(): Pose3D | null {
    return this.currentPose;
  }

  /**
   * Captures the current rendered scene as a base64-encoded PNG image.
   * Useful for debugging, saving AR experiences, or creating thumbnails.
   *
   * @returns Base64-encoded PNG data URL of the current scene
   *
   * @example
   * ```typescript
   * const arRenderer = new ARRenderer(canvas, 640, 480);
   * arRenderer.createModel("cube", { type: "cube" });
   * arRenderer.startRendering();
   *
   * // Capture screenshot after a delay to ensure rendering
   * setTimeout(() => {
   *   const screenshot = arRenderer.takeScreenshot();
   *
   *   // Create download link
   *   const link = document.createElement('a');
   *   link.download = 'ar_scene.png';
   *   link.href = screenshot;
   *   link.click();
   *
   *   // Or display in image element
   *   const img = document.createElement('img');
   *   img.src = screenshot;
   *   document.body.appendChild(img);
   * }, 1000);
   * ```
   *
   * @public
   */
  takeScreenshot(): string {
    return this.renderer.domElement.toDataURL("image/png");
  }

  /**
   * Cleans up all renderer resources and prepares for garbage collection.
   * Stops rendering, removes all models, clears the scene, and disposes of WebGL resources.
   * Should be called when the AR renderer is no longer needed to prevent memory leaks.
   *
   * @example
   * ```typescript
   * const arRenderer = new ARRenderer(canvas, 640, 480);
   *
   * // Use renderer...
   *
   * // Clean up when done
   * arRenderer.dispose();
   *
   * // Renderer is no longer usable after dispose
   * ```
   *
   * @remarks
   * - Stops the rendering loop immediately
   * - Removes and disposes all AR models and their resources
   * - Clears the Three.js scene of all objects
   * - Disposes the WebGL renderer and frees GPU memory
   * - Marks renderer as uninitialized
   * - After disposal, create a new renderer instance if needed
   *
   * @public
   */
  dispose(): void {
    this.stopRendering();

    // Dispose all models
    this.models.forEach((model, id) => {
      this.removeModel(id);
    });

    // Dispose scene
    this.scene.clear();

    // Dispose renderer
    this.renderer.dispose();

    this.isInitialized = false;
  }
}
