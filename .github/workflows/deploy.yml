name: Deploy to EC2

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup SSH
      run: |
        mkdir -p ~/.ssh
        chmod 700 ~/.ssh
        
        echo "${{ secrets.EC2_SSH_KEY }}" | base64 -d > ~/.ssh/ec2_key
        chmod 600 ~/.ssh/ec2_key
        
        eval $(ssh-agent -s)
        ssh-add ~/.ssh/ec2_key
    
    - name: Add EC2 to known hosts
      run: |
        ssh-keyscan -H ${{ secrets.EC2_HOST }} >> ~/.ssh/known_hosts
        
    - name: Deploy to EC2
      run: |
        ssh -i ~/.ssh/ec2_key -o StrictHostKeyChecking=no ${{ secrets.EC2_USER }}@${{ secrets.EC2_HOST }} << 'EOF'
          set -e
          echo "🚀 Starting deployment..."
          
          cd ${{ secrets.EC2_PROJECT_PATH }}
          
          echo "📥 Pulling latest changes..."
          git fetch origin
          git reset --hard origin/main
          
          chmod +x deploy.sh
          
          echo "🐳 Running deployment..."
          ./deploy.sh
          
          echo "✅ Deployment completed successfully!"
        EOF

    - name: Verify deployment
      run: |
        echo "🔍 Verifying deployment..."
        sleep 30
        
        response=$(curl -s -o /dev/null -w "%{http_code}" http://${{ secrets.EC2_HOST }}:3000 || echo "000")
        
        if [ "$response" = "200" ]; then
          echo "✅ Web app is healthy and responding"
          echo "🌐 App available at: http://${{ secrets.EC2_HOST }}:3000"
        else
          echo "❌ Web app health check failed (HTTP $response)"
          exit 1
        fi