<!doctype html>
<html lang="en">
    <head>
        <title>Three.js Keyframe Animation</title>
        <meta charset="utf-8" />
        <meta
            name="viewport"
            content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0"
        />
        <style>
            body {
                margin: 0;
                background-color: #bfe3dd;
                color: #000;
                font-family: Arial, sans-serif;
                overflow: hidden;
            }

            #info {
                position: absolute;
                top: 10px;
                width: 100%;
                text-align: center;
                z-index: 100;
                display: block;
            }

            #info a {
                color: #2983ff;
                text-decoration: none;
            }

            #info a:hover {
                text-decoration: underline;
            }

            #stats {
                position: absolute;
                top: 0;
                left: 0;
            }
        </style>
    </head>
    <body>
        <div id="container"></div>
        <div id="info">
            <a href="https://threejs.org" target="_blank" rel="noopener"
                >three.js</a
            >
            - Keyframe Animation Demo<br />
            Animated city scene with moving vehicles and rotating elements
        </div>

        <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
        <script>
            let scene, camera, renderer;
            let clock,
                mixers = [];
            let stats;

            // Animation variables
            let car1, car2, helicopter, windmill, ferrisWheel;

            init();
            animate();

            function init() {
                // Clock for animation timing
                clock = new THREE.Clock();

                // Container
                const container = document.getElementById("container");

                // Renderer
                renderer = new THREE.WebGLRenderer({ antialias: true });
                renderer.setPixelRatio(window.devicePixelRatio);
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.shadowMap.enabled = true;
                renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                container.appendChild(renderer.domElement);

                // Scene
                scene = new THREE.Scene();
                scene.background = new THREE.Color(0xbfe3dd);
                scene.fog = new THREE.Fog(0xbfe3dd, 10, 50);

                // Camera
                camera = new THREE.PerspectiveCamera(
                    40,
                    window.innerWidth / window.innerHeight,
                    0.1,
                    100,
                );
                camera.position.set(15, 8, 15);
                camera.lookAt(0, 0, 0);

                // Lights
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
                scene.add(ambientLight);

                const directionalLight = new THREE.DirectionalLight(
                    0xffffff,
                    0.8,
                );
                directionalLight.position.set(10, 20, 5);
                directionalLight.castShadow = true;
                directionalLight.shadow.camera.near = 0.1;
                directionalLight.shadow.camera.far = 50;
                directionalLight.shadow.camera.left = -20;
                directionalLight.shadow.camera.right = 20;
                directionalLight.shadow.camera.top = 20;
                directionalLight.shadow.camera.bottom = -20;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                scene.add(directionalLight);

                // Ground
                const groundGeometry = new THREE.PlaneGeometry(40, 40);
                const groundMaterial = new THREE.MeshLambertMaterial({
                    color: 0x7cba7c,
                });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.receiveShadow = true;
                scene.add(ground);

                // Roads
                createRoads();

                // Buildings
                createBuildings();

                // Animated objects
                createAnimatedObjects();

                // Create animations
                createAnimations();

                // Mouse controls
                let mouseX = 0,
                    mouseY = 0;
                document.addEventListener("mousemove", (e) => {
                    mouseX = (e.clientX / window.innerWidth) * 2 - 1;
                    mouseY = -(e.clientY / window.innerHeight) * 2 + 1;
                });

                // Window resize
                window.addEventListener("resize", onWindowResize);

                // Simple orbit controls
                let isMouseDown = false;
                let mouseDownX = 0,
                    mouseDownY = 0;
                let cameraTheta = (45 * Math.PI) / 180;
                let cameraPhi = (30 * Math.PI) / 180;
                let cameraRadius = 25;

                renderer.domElement.addEventListener("mousedown", (e) => {
                    isMouseDown = true;
                    mouseDownX = e.clientX;
                    mouseDownY = e.clientY;
                });

                document.addEventListener("mousemove", (e) => {
                    if (isMouseDown) {
                        const deltaX = e.clientX - mouseDownX;
                        const deltaY = e.clientY - mouseDownY;

                        cameraTheta -= deltaX * 0.01;
                        cameraPhi = Math.max(
                            0.1,
                            Math.min(
                                Math.PI / 2 - 0.1,
                                cameraPhi - deltaY * 0.01,
                            ),
                        );

                        camera.position.x =
                            cameraRadius *
                            Math.sin(cameraPhi) *
                            Math.cos(cameraTheta);
                        camera.position.y = cameraRadius * Math.cos(cameraPhi);
                        camera.position.z =
                            cameraRadius *
                            Math.sin(cameraPhi) *
                            Math.sin(cameraTheta);
                        camera.lookAt(0, 0, 0);

                        mouseDownX = e.clientX;
                        mouseDownY = e.clientY;
                    }
                });

                document.addEventListener("mouseup", () => {
                    isMouseDown = false;
                });

                // Mouse wheel zoom
                renderer.domElement.addEventListener("wheel", (e) => {
                    cameraRadius = Math.max(
                        10,
                        Math.min(40, cameraRadius + e.deltaY * 0.01),
                    );
                    camera.position.x =
                        cameraRadius *
                        Math.sin(cameraPhi) *
                        Math.cos(cameraTheta);
                    camera.position.y = cameraRadius * Math.cos(cameraPhi);
                    camera.position.z =
                        cameraRadius *
                        Math.sin(cameraPhi) *
                        Math.sin(cameraTheta);
                    camera.lookAt(0, 0, 0);
                });
            }

            function createRoads() {
                const roadMaterial = new THREE.MeshLambertMaterial({
                    color: 0x333333,
                });

                // Main roads
                const roadGeometry = new THREE.BoxGeometry(40, 0.1, 4);
                const road1 = new THREE.Mesh(roadGeometry, roadMaterial);
                road1.position.y = 0.05;
                road1.receiveShadow = true;
                scene.add(road1);

                const road2 = new THREE.Mesh(roadGeometry, roadMaterial);
                road2.position.y = 0.05;
                road2.rotation.y = Math.PI / 2;
                road2.receiveShadow = true;
                scene.add(road2);

                // Road lines
                const lineMaterial = new THREE.MeshBasicMaterial({
                    color: 0xffff00,
                });
                const lineGeometry = new THREE.BoxGeometry(2, 0.02, 0.2);

                for (let i = -18; i <= 18; i += 4) {
                    const line = new THREE.Mesh(lineGeometry, lineMaterial);
                    line.position.set(i, 0.1, 0);
                    scene.add(line);

                    const line2 = new THREE.Mesh(lineGeometry, lineMaterial);
                    line2.position.set(0, 0.1, i);
                    line2.rotation.y = Math.PI / 2;
                    scene.add(line2);
                }
            }

            function createBuildings() {
                const buildingMaterials = [
                    new THREE.MeshLambertMaterial({ color: 0x8b7355 }),
                    new THREE.MeshLambertMaterial({ color: 0x696969 }),
                    new THREE.MeshLambertMaterial({ color: 0xcd853f }),
                    new THREE.MeshLambertMaterial({ color: 0x708090 }),
                ];

                // Create buildings in quadrants
                const positions = [
                    { x: 8, z: 8 },
                    { x: 12, z: 10 },
                    { x: 10, z: 14 },
                    { x: -8, z: 8 },
                    { x: -12, z: 10 },
                    { x: -10, z: 14 },
                    { x: 8, z: -8 },
                    { x: 12, z: -10 },
                    { x: 10, z: -14 },
                    { x: -8, z: -8 },
                    { x: -12, z: -10 },
                    { x: -10, z: -14 },
                ];

                positions.forEach((pos, i) => {
                    const height = 3 + Math.random() * 4;
                    const buildingGeometry = new THREE.BoxGeometry(
                        2 + Math.random() * 2,
                        height,
                        2 + Math.random() * 2,
                    );
                    const building = new THREE.Mesh(
                        buildingGeometry,
                        buildingMaterials[i % buildingMaterials.length],
                    );
                    building.position.set(pos.x, height / 2, pos.z);
                    building.castShadow = true;
                    building.receiveShadow = true;
                    scene.add(building);

                    // Windows
                    const windowMaterial = new THREE.MeshBasicMaterial({
                        color: 0xffff00,
                    });
                    const windowGeometry = new THREE.BoxGeometry(0.3, 0.3, 0.1);

                    for (let floor = 0; floor < Math.floor(height); floor++) {
                        for (let side = 0; side < 4; side++) {
                            const window = new THREE.Mesh(
                                windowGeometry,
                                windowMaterial,
                            );
                            const angle = (side * Math.PI) / 2;
                            const radius = 1.2;
                            window.position.set(
                                pos.x + Math.cos(angle) * radius,
                                floor + 0.5,
                                pos.z + Math.sin(angle) * radius,
                            );
                            window.rotation.y = angle;
                            scene.add(window);
                        }
                    }
                });
            }

            function createAnimatedObjects() {
                // Car 1
                const car1Group = new THREE.Group();
                const car1Body = new THREE.Mesh(
                    new THREE.BoxGeometry(2, 0.8, 1),
                    new THREE.MeshLambertMaterial({ color: 0xff0000 }),
                );
                car1Body.position.y = 0.4;
                car1Body.castShadow = true;
                car1Group.add(car1Body);

                const car1Roof = new THREE.Mesh(
                    new THREE.BoxGeometry(1.2, 0.4, 0.8),
                    new THREE.MeshLambertMaterial({ color: 0x8b0000 }),
                );
                car1Roof.position.y = 0.9;
                car1Roof.castShadow = true;
                car1Group.add(car1Roof);

                // Wheels
                const wheelGeometry = new THREE.CylinderGeometry(
                    0.2,
                    0.2,
                    0.2,
                    16,
                );
                const wheelMaterial = new THREE.MeshLambertMaterial({
                    color: 0x000000,
                });

                const wheelPositions = [
                    { x: 0.6, z: 0.5 },
                    { x: 0.6, z: -0.5 },
                    { x: -0.6, z: 0.5 },
                    { x: -0.6, z: -0.5 },
                ];

                wheelPositions.forEach((pos) => {
                    const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                    wheel.position.set(pos.x, 0.2, pos.z);
                    wheel.rotation.z = Math.PI / 2;
                    wheel.castShadow = true;
                    car1Group.add(wheel);
                });

                car1 = car1Group;
                scene.add(car1);

                // Car 2
                const car2Group = new THREE.Group();
                const car2Body = new THREE.Mesh(
                    new THREE.BoxGeometry(2, 0.8, 1),
                    new THREE.MeshLambertMaterial({ color: 0x0000ff }),
                );
                car2Body.position.y = 0.4;
                car2Body.castShadow = true;
                car2Group.add(car2Body);

                const car2Roof = new THREE.Mesh(
                    new THREE.BoxGeometry(1.2, 0.4, 0.8),
                    new THREE.MeshLambertMaterial({ color: 0x00008b }),
                );
                car2Roof.position.y = 0.9;
                car2Roof.castShadow = true;
                car2Group.add(car2Roof);

                wheelPositions.forEach((pos) => {
                    const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                    wheel.position.set(pos.x, 0.2, pos.z);
                    wheel.rotation.z = Math.PI / 2;
                    wheel.castShadow = true;
                    car2Group.add(wheel);
                });

                car2 = car2Group;
                scene.add(car2);

                // Helicopter
                const helicopterGroup = new THREE.Group();
                const heliBody = new THREE.Mesh(
                    new THREE.SphereGeometry(1, 16, 8),
                    new THREE.MeshLambertMaterial({ color: 0x006400 }),
                );
                heliBody.scale.z = 1.5;
                heliBody.castShadow = true;
                helicopterGroup.add(heliBody);

                const heliTail = new THREE.Mesh(
                    new THREE.CylinderGeometry(0.3, 0.1, 3),
                    new THREE.MeshLambertMaterial({ color: 0x008000 }),
                );
                heliTail.position.set(-2, 0, 0);
                heliTail.rotation.z = Math.PI / 2;
                heliTail.castShadow = true;
                helicopterGroup.add(heliTail);

                // Main rotor
                const rotorGroup = new THREE.Group();
                const rotorBlade = new THREE.Mesh(
                    new THREE.BoxGeometry(8, 0.1, 0.3),
                    new THREE.MeshLambertMaterial({ color: 0x333333 }),
                );
                rotorGroup.add(rotorBlade);

                const rotorBlade2 = new THREE.Mesh(
                    new THREE.BoxGeometry(8, 0.1, 0.3),
                    new THREE.MeshLambertMaterial({ color: 0x333333 }),
                );
                rotorBlade2.rotation.y = Math.PI / 2;
                rotorGroup.add(rotorBlade2);

                rotorGroup.position.y = 1;
                helicopterGroup.add(rotorGroup);

                helicopter = helicopterGroup;
                helicopter.position.y = 10;
                scene.add(helicopter);

                // Store rotor reference for animation
                helicopter.rotor = rotorGroup;

                // Windmill
                const windmillGroup = new THREE.Group();
                const windmillBase = new THREE.Mesh(
                    new THREE.CylinderGeometry(0.5, 1, 6),
                    new THREE.MeshLambertMaterial({ color: 0x8b4513 }),
                );
                windmillBase.position.y = 3;
                windmillBase.castShadow = true;
                windmillGroup.add(windmillBase);

                const bladesGroup = new THREE.Group();
                for (let i = 0; i < 4; i++) {
                    const blade = new THREE.Mesh(
                        new THREE.BoxGeometry(3, 0.2, 0.5),
                        new THREE.MeshLambertMaterial({ color: 0xffffff }),
                    );
                    blade.position.x = 1.5;
                    blade.rotation.z = (i * Math.PI) / 2;
                    bladesGroup.add(blade);
                }
                bladesGroup.position.y = 6;
                windmillGroup.add(bladesGroup);

                windmill = windmillGroup;
                windmill.blades = bladesGroup;
                windmill.position.set(15, 0, 15);
                scene.add(windmill);

                // Ferris Wheel
                const ferrisGroup = new THREE.Group();
                const wheelFrame = new THREE.Group();

                // Wheel structure
                const ringGeometry = new THREE.TorusGeometry(4, 0.1, 8, 32);
                const ringMaterial = new THREE.MeshLambertMaterial({
                    color: 0x4169e1,
                });
                const ring = new THREE.Mesh(ringGeometry, ringMaterial);
                wheelFrame.add(ring);

                // Spokes
                for (let i = 0; i < 8; i++) {
                    const spoke = new THREE.Mesh(
                        new THREE.BoxGeometry(8, 0.1, 0.1),
                        ringMaterial,
                    );
                    spoke.rotation.z = (i * Math.PI) / 4;
                    wheelFrame.add(spoke);
                }

                // Cabins
                const cabins = [];
                for (let i = 0; i < 8; i++) {
                    const cabin = new THREE.Mesh(
                        new THREE.BoxGeometry(0.8, 1, 0.8),
                        new THREE.MeshLambertMaterial({ color: 0xff69b4 }),
                    );
                    const angle = (i * Math.PI * 2) / 8;
                    cabin.position.x = Math.cos(angle) * 4;
                    cabin.position.y = Math.sin(angle) * 4;
                    cabin.castShadow = true;
                    wheelFrame.add(cabin);
                    cabins.push(cabin);
                }

                wheelFrame.position.y = 5;
                ferrisGroup.add(wheelFrame);

                // Support structure
                const support1 = new THREE.Mesh(
                    new THREE.BoxGeometry(0.3, 10, 0.3),
                    new THREE.MeshLambertMaterial({ color: 0x696969 }),
                );
                support1.position.set(2, 5, 0);
                support1.rotation.z = -0.2;
                support1.castShadow = true;
                ferrisGroup.add(support1);

                const support2 = new THREE.Mesh(
                    new THREE.BoxGeometry(0.3, 10, 0.3),
                    new THREE.MeshLambertMaterial({ color: 0x696969 }),
                );
                support2.position.set(-2, 5, 0);
                support2.rotation.z = 0.2;
                support2.castShadow = true;
                ferrisGroup.add(support2);

                ferrisWheel = ferrisGroup;
                ferrisWheel.wheel = wheelFrame;
                ferrisWheel.cabins = cabins;
                ferrisWheel.position.set(-15, 0, -15);
                scene.add(ferrisWheel);
            }

            function createAnimations() {
                // Car 1 path animation
                const car1Mixer = new THREE.AnimationMixer(car1);
                const car1Track = new THREE.VectorKeyframeTrack(
                    ".position",
                    [0, 2, 4, 6, 8],
                    [
                        -15, 0.1, 0, 0, 0.1, 0, 15, 0.1, 0, 0, 0.1, 0, -15, 0.1,
                        0,
                    ],
                );

                const car1RotationTrack = new THREE.QuaternionKeyframeTrack(
                    ".quaternion",
                    [0, 2, 4, 6, 8],
                    [
                        0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0,
                        1,
                    ],
                );

                const car1Clip = new THREE.AnimationClip("car1Move", 8, [
                    car1Track,
                    car1RotationTrack,
                ]);
                const car1Action = car1Mixer.clipAction(car1Clip);
                car1Action.play();
                mixers.push(car1Mixer);

                // Car 2 path animation
                const car2Mixer = new THREE.AnimationMixer(car2);
                const car2Track = new THREE.VectorKeyframeTrack(
                    ".position",
                    [0, 2, 4, 6, 8],
                    [
                        0, 0.1, -15, 0, 0.1, 0, 0, 0.1, 15, 0, 0.1, 0, 0, 0.1,
                        -15,
                    ],
                );

                const car2RotationTrack = new THREE.QuaternionKeyframeTrack(
                    ".quaternion",
                    [0, 2, 4, 6, 8],
                    [
                        0, 0.707, 0, 0.707, 0, 0.707, 0, 0.707, 0, -0.707, 0,
                        0.707, 0, -0.707, 0, 0.707, 0, 0.707, 0, 0.707,
                    ],
                );

                const car2Clip = new THREE.AnimationClip("car2Move", 8, [
                    car2Track,
                    car2RotationTrack,
                ]);
                const car2Action = car2Mixer.clipAction(car2Clip);
                car2Action.play();
                mixers.push(car2Mixer);

                // Helicopter path animation
                const heliMixer = new THREE.AnimationMixer(helicopter);
                const heliTrack = new THREE.VectorKeyframeTrack(
                    ".position",
                    [0, 3, 6, 9, 12],
                    [
                        -10, 10, -10, 10, 12, -10, 10, 11, 10, -10, 13, 10, -10,
                        10, -10,
                    ],
                );

                const heliClip = new THREE.AnimationClip("heliMove", 12, [
                    heliTrack,
                ]);
                const heliAction = heliMixer.clipAction(heliClip);
                heliAction.play();
                mixers.push(heliMixer);
            }

            function onWindowResize() {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            }

            function animate() {
                requestAnimationFrame(animate);

                const delta = clock.getDelta();
                const time = clock.getElapsedTime();

                // Update animation mixers
                mixers.forEach((mixer) => {
                    mixer.update(delta);
                });

                // Rotate helicopter rotor
                if (helicopter && helicopter.rotor) {
                    helicopter.rotor.rotation.y += delta * 10;
                }

                // Rotate windmill blades
                if (windmill && windmill.blades) {
                    windmill.blades.rotation.z += delta * 0.5;
                }

                // Rotate ferris wheel
                if (ferrisWheel && ferrisWheel.wheel) {
                    ferrisWheel.wheel.rotation.z += delta * 0.1;

                    // Keep cabins upright
                    ferrisWheel.cabins.forEach((cabin, i) => {
                        cabin.rotation.z = -ferrisWheel.wheel.rotation.z;
                    });
                }

                // Animate some building lights
                const lights = scene.children.filter(
                    (child) =>
                        child.material &&
                        child.material.color &&
                        child.geometry &&
                        child.geometry.type === "BoxGeometry" &&
                        child.material.color.getHex() === 0xffff00,
                );

                lights.forEach((light, i) => {
                    light.material.opacity = 0.5 + Math.sin(time * 2 + i) * 0.5;
                    light.material.transparent = true;
                });

                renderer.render(scene, camera);
            }
        </script>
    </body>
</html>
