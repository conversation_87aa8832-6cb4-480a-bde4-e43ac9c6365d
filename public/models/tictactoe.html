<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Tic Tac Toe</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
        }

        .game-container {
            text-align: center;
            padding: 20px;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            color: #fff;
        }

        #game-message {
            font-size: 1.5rem;
            margin-bottom: 20px;
            padding: 10px 20px;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            display: inline-block;
            min-width: 200px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        #three-container {
            margin: 20px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
            display: inline-block;
        }

        #three-container canvas {
            display: block;
            border-radius: 15px;
        }

        #restart-btn {
            font-size: 1.2rem;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(45deg, #ff6b6b, #ffa500);
            color: white;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        #restart-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            background: linear-gradient(45deg, #ff5252, #ff9800);
        }

        #restart-btn:active {
            transform: translateY(0);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            h1 {
                font-size: 2rem;
            }
            
            #game-message {
                font-size: 1.2rem;
            }
            
            #restart-btn {
                font-size: 1rem;
                padding: 10px 25px;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>3D Tic Tac Toe</h1>
        <div id="game-message">Player X's Turn</div>
        <div id="three-container"></div>
        <button id="restart-btn">Restart Game</button>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        // Game variables
        let scene, camera, renderer, raycaster, mouse;
        let gameBoard = [];
        let currentPlayer = 'X';
        let gameActive = true;
        let cells = [];
        let winningLine = [];
        let mousePosition = { x: 0, y: 0 };
        let targetRotation = { x: 0, y: 0 };
        let currentRotation = { x: 0, y: 0 };

        // Materials for different states
        const materials = {
            empty: new THREE.MeshLambertMaterial({ color: 0x444444, transparent: true, opacity: 0.8 }),
            hover: new THREE.MeshLambertMaterial({ color: 0x666666, transparent: true, opacity: 0.9 }),
            X: new THREE.MeshLambertMaterial({ color: 0xff4444 }),
            O: new THREE.MeshLambertMaterial({ color: 0x4444ff }),
            winning: new THREE.MeshLambertMaterial({ color: 0x44ff44, emissive: 0x004400 })
        };

        // Initialize the game
        function init() {
            // Initialize game board
            gameBoard = Array(9).fill(null);
            
            // Create scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x222222);
            
            // Create camera
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 0, 8);
            
            // Create renderer
            const container = document.getElementById('three-container');
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(600, 600);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            container.appendChild(renderer.domElement);
            
            // Add lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);
            
            // Create raycaster for mouse interaction
            raycaster = new THREE.Raycaster();
            mouse = new THREE.Vector2();
            
            // Create game board
            createGameBoard();
            
            // Add event listeners
            renderer.domElement.addEventListener('mousemove', onMouseMove);
            renderer.domElement.addEventListener('mouseleave', onMouseLeave);
            renderer.domElement.addEventListener('click', onMouseClick);
            document.getElementById('restart-btn').addEventListener('click', restartGame);
            
            // Start render loop
            animate();
        }

        // Create the 3D game board
        function createGameBoard() {
            cells = [];
            
            for (let i = 0; i < 9; i++) {
                const row = Math.floor(i / 3);
                const col = i % 3;
                
                // Create cube geometry
                const geometry = new THREE.BoxGeometry(1.8, 1.8, 0.3);
                const cube = new THREE.Mesh(geometry, materials.empty.clone());
                
                // Position the cube
                cube.position.set(
                    (col - 1) * 2.2,
                    -(row - 1) * 2.2,
                    0
                );
                
                cube.castShadow = true;
                cube.receiveShadow = true;
                cube.userData = { index: i, state: 'empty' };
                
                scene.add(cube);
                cells.push(cube);
            }
        }

        // Handle mouse movement for hover effects and gyroscope
        function onMouseMove(event) {
            const rect = renderer.domElement.getBoundingClientRect();
            mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
            mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
            
            // Update mouse position for gyroscope effect
            mousePosition.x = ((event.clientX - rect.left) / rect.width - 0.5) * 2;
            mousePosition.y = ((event.clientY - rect.top) / rect.height - 0.5) * 2;
            
            if (!gameActive) return;
            
            raycaster.setFromCamera(mouse, camera);
            const intersects = raycaster.intersectObjects(cells);
            
            // Reset all cells to their base state
            cells.forEach(cell => {
                if (cell.userData.state === 'empty') {
                    cell.material = materials.empty;
                }
            });
            
            // Highlight hovered cell
            if (intersects.length > 0) {
                const cell = intersects[0].object;
                if (cell.userData.state === 'empty') {
                    cell.material = materials.hover;
                }
            }
        }

        // Handle mouse leave for gyroscope reset
        function onMouseLeave() {
            mousePosition.x = 0;
            mousePosition.y = 0;
        }

        // Handle mouse clicks
        function onMouseClick(event) {
            if (!gameActive) return;
            
            const rect = renderer.domElement.getBoundingClientRect();
            mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
            mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
            
            raycaster.setFromCamera(mouse, camera);
            const intersects = raycaster.intersectObjects(cells);
            
            if (intersects.length > 0) {
                const cell = intersects[0].object;
                const index = cell.userData.index;
                
                if (gameBoard[index] === null) {
                    makeMove(index);
                }
            }
        }

        // Make a move
        function makeMove(index) {
            gameBoard[index] = currentPlayer;
            const cell = cells[index];
            
            // Update visual representation
            if (currentPlayer === 'X') {
                cell.material = materials.X;
                createXSymbol(cell.position);
            } else {
                cell.material = materials.O;
                createOSymbol(cell.position);
            }
            
            cell.userData.state = currentPlayer;
            
            // Check for win or draw
            const winner = checkWinner();
            if (winner) {
                gameActive = false;
                highlightWinningLine();
                updateMessage(`Player ${winner} Wins!`);
            } else if (gameBoard.every(cell => cell !== null)) {
                gameActive = false;
                updateMessage("It's a Draw!");
            } else {
                currentPlayer = currentPlayer === 'X' ? 'O' : 'X';
                updateMessage(`Player ${currentPlayer}'s Turn`);
            }
        }

        // Create X symbol
        function createXSymbol(position) {
            const group = new THREE.Group();
            
            // Create two crossed bars
            const barGeometry = new THREE.BoxGeometry(2, 0.2, 0.2);
            const bar1 = new THREE.Mesh(barGeometry, materials.X);
            const bar2 = new THREE.Mesh(barGeometry, materials.X);
            
            bar1.rotation.z = Math.PI / 4;
            bar2.rotation.z = -Math.PI / 4;
            
            group.add(bar1);
            group.add(bar2);
            group.position.copy(position);
            group.position.z += 0.3;
            
            scene.add(group);
        }

        // Create O symbol
        function createOSymbol(position) {
            const ringGeometry = new THREE.RingGeometry(0.5, 0.8, 32);
            const ring = new THREE.Mesh(ringGeometry, materials.O);
            ring.position.copy(position);
            ring.position.z += 0.3;
            scene.add(ring);
        }

        // Check for winner
        function checkWinner() {
            const winPatterns = [
                [0, 1, 2], [3, 4, 5], [6, 7, 8], // Rows
                [0, 3, 6], [1, 4, 7], [2, 5, 8], // Columns
                [0, 4, 8], [2, 4, 6] // Diagonals
            ];
            
            for (let pattern of winPatterns) {
                const [a, b, c] = pattern;
                if (gameBoard[a] && gameBoard[a] === gameBoard[b] && gameBoard[a] === gameBoard[c]) {
                    winningLine = pattern;
                    return gameBoard[a];
                }
            }
            
            return null;
        }

        // Highlight winning line
        function highlightWinningLine() {
            winningLine.forEach(index => {
                cells[index].material = materials.winning;
            });
        }

        // Update message
        function updateMessage(message) {
            document.getElementById('game-message').textContent = message;
        }

        // Restart game
        function restartGame() {
            // Clear the scene of X and O symbols
            const objectsToRemove = [];
            scene.traverse((object) => {
                if (object !== camera && !cells.includes(object) && object.type !== 'AmbientLight' && object.type !== 'DirectionalLight') {
                    if (object.geometry && (object.geometry.type === 'RingGeometry' || object.parent.type === 'Group')) {
                        objectsToRemove.push(object.parent || object);
                    }
                }
            });
            
            objectsToRemove.forEach(object => {
                scene.remove(object);
            });
            
            // Reset game state
            gameBoard = Array(9).fill(null);
            currentPlayer = 'X';
            gameActive = true;
            winningLine = [];
            
            // Reset cell materials
            cells.forEach(cell => {
                cell.material = materials.empty.clone();
                cell.userData.state = 'empty';
            });
            
            updateMessage("Player X's Turn");
        }

        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            
            // Smooth gyroscope effect based on mouse position
            targetRotation.x = mousePosition.y * 0.3;
            targetRotation.y = mousePosition.x * 0.3;
            
            // Smooth interpolation for natural movement
            const easing = 0.05;
            currentRotation.x += (targetRotation.x - currentRotation.x) * easing;
            currentRotation.y += (targetRotation.y - currentRotation.y) * easing;
            
            scene.rotation.x = currentRotation.x;
            scene.rotation.y = currentRotation.y;
            
            renderer.render(scene, camera);
        }

        // Handle window resize
        function onWindowResize() {
            camera.aspect = 600 / 600;
            camera.updateProjectionMatrix();
            renderer.setSize(600, 600);
        }

        // Start the game when the page loads
        window.addEventListener('load', init);
        window.addEventListener('resize', onWindowResize);
    </script>
</body>
</html> 