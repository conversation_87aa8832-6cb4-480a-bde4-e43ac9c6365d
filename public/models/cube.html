<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Three.js Dice Game</title>
        <style>
            body {
                margin: 0;
                padding: 0;
                background: #1a1a2e;
                overflow: hidden;
                font-family: Arial, sans-serif;
                touch-action: none;
            }
            canvas {
                display: block;
            }
            #info {
                position: absolute;
                top: 10px;
                left: 10px;
                color: white;
                font-size: 16px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
                z-index: 100;
            }
            #result {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 72px;
                font-weight: bold;
                color: #ffd700;
                text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.8);
                opacity: 0;
                transition: opacity 0.5s;
                z-index: 100;
                pointer-events: none;
            }
            #result.show {
                opacity: 1;
            }
            #instructions {
                position: absolute;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                color: white;
                text-align: center;
                font-size: 18px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            }
        </style>
    </head>
    <body>
        <div id="info">Click and drag to flick the dice!</div>
        <div id="result"></div>
        <div id="instructions">
            Flick the dice to roll - Get a 6 for fireworks! 🎆
        </div>

        <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
        <script>
            // Scene setup
            const scene = new THREE.Scene();
            scene.background = new THREE.Color(0x1a1a2e);

            const camera = new THREE.PerspectiveCamera(
                75,
                window.innerWidth / window.innerHeight,
                0.1,
                1000,
            );
            camera.position.set(0, 12, 20);
            camera.lookAt(0, 0, 0);

            const renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            document.body.appendChild(renderer.domElement);

            // Lights
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 20, 5);
            directionalLight.castShadow = true;
            directionalLight.shadow.camera.near = 0.1;
            directionalLight.shadow.camera.far = 50;
            directionalLight.shadow.camera.left = -10;
            directionalLight.shadow.camera.right = 10;
            directionalLight.shadow.camera.top = 10;
            directionalLight.shadow.camera.bottom = -10;
            scene.add(directionalLight);

            // Ground plane
            const groundGeometry = new THREE.PlaneGeometry(30, 30);
            const groundMaterial = new THREE.MeshStandardMaterial({
                color: 0x0f3460,
                roughness: 0.8,
                metalness: 0.2,
            });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.position.y = -2;
            ground.receiveShadow = true;
            scene.add(ground);

            // Create walls (frame boundaries)
            const wallThickness = 1;
            const wallHeight = 8;
            const wallLength = 30;
            const wallMaterial = new THREE.MeshStandardMaterial({
                color: 0x2c3e50,
                roughness: 0.6,
                metalness: 0.3,
                transparent: true,
                opacity: 0.8,
            });

            // Back wall
            const backWall = new THREE.Mesh(
                new THREE.BoxGeometry(wallLength, wallHeight, wallThickness),
                wallMaterial,
            );
            backWall.position.set(0, wallHeight / 2 - 2, -wallLength / 2);
            backWall.castShadow = true;
            backWall.receiveShadow = true;
            scene.add(backWall);

            // Front wall (transparent)
            const frontWall = new THREE.Mesh(
                new THREE.BoxGeometry(wallLength, wallHeight, wallThickness),
                new THREE.MeshStandardMaterial({
                    color: 0x2c3e50,
                    roughness: 0.6,
                    metalness: 0.3,
                    transparent: true,
                    opacity: 0.1,
                }),
            );
            frontWall.position.set(0, wallHeight / 2 - 2, wallLength / 2);
            scene.add(frontWall);

            // Left wall
            const leftWall = new THREE.Mesh(
                new THREE.BoxGeometry(wallThickness, wallHeight, wallLength),
                wallMaterial,
            );
            leftWall.position.set(-wallLength / 2, wallHeight / 2 - 2, 0);
            leftWall.castShadow = true;
            leftWall.receiveShadow = true;
            scene.add(leftWall);

            // Right wall
            const rightWall = new THREE.Mesh(
                new THREE.BoxGeometry(wallThickness, wallHeight, wallLength),
                wallMaterial,
            );
            rightWall.position.set(wallLength / 2, wallHeight / 2 - 2, 0);
            rightWall.castShadow = true;
            rightWall.receiveShadow = true;
            scene.add(rightWall);

            // Add decorative frame edges
            const edgeMaterial = new THREE.MeshStandardMaterial({
                color: 0x34495e,
                roughness: 0.4,
                metalness: 0.6,
            });

            const edgeSize = 0.5;
            // Top edges
            const edges = [
                {
                    pos: [0, wallHeight - 2, -wallLength / 2],
                    size: [wallLength, edgeSize, edgeSize],
                },
                {
                    pos: [0, wallHeight - 2, wallLength / 2],
                    size: [wallLength, edgeSize, edgeSize],
                },
                {
                    pos: [-wallLength / 2, wallHeight - 2, 0],
                    size: [edgeSize, edgeSize, wallLength],
                },
                {
                    pos: [wallLength / 2, wallHeight - 2, 0],
                    size: [edgeSize, edgeSize, wallLength],
                },
            ];

            edges.forEach((edge) => {
                const mesh = new THREE.Mesh(
                    new THREE.BoxGeometry(...edge.size),
                    edgeMaterial,
                );
                mesh.position.set(...edge.pos);
                mesh.castShadow = true;
                scene.add(mesh);
            });

            // Create dice
            const diceSize = 2;
            const diceGeometry = new THREE.BoxGeometry(
                diceSize,
                diceSize,
                diceSize,
            );

            // Create canvas texture for dice faces
            function createDiceFace(number) {
                const canvas = document.createElement("canvas");
                canvas.width = 256;
                canvas.height = 256;
                const ctx = canvas.getContext("2d");

                // Background
                ctx.fillStyle = "#ffffff";
                ctx.fillRect(0, 0, 256, 256);

                // Border
                ctx.strokeStyle = "#000000";
                ctx.lineWidth = 8;
                ctx.strokeRect(4, 4, 248, 248);

                // Dots
                ctx.fillStyle = "#000000";
                const dotSize = 30;
                const positions = {
                    1: [[128, 128]],
                    2: [
                        [64, 64],
                        [192, 192],
                    ],
                    3: [
                        [64, 64],
                        [128, 128],
                        [192, 192],
                    ],
                    4: [
                        [64, 64],
                        [192, 64],
                        [64, 192],
                        [192, 192],
                    ],
                    5: [
                        [64, 64],
                        [192, 64],
                        [128, 128],
                        [64, 192],
                        [192, 192],
                    ],
                    6: [
                        [64, 64],
                        [192, 64],
                        [64, 128],
                        [192, 128],
                        [64, 192],
                        [192, 192],
                    ],
                };

                positions[number].forEach((pos) => {
                    ctx.beginPath();
                    ctx.arc(pos[0], pos[1], dotSize / 2, 0, Math.PI * 2);
                    ctx.fill();
                });

                return new THREE.CanvasTexture(canvas);
            }

            // Create materials for each face
            const diceMaterials = [
                new THREE.MeshStandardMaterial({ map: createDiceFace(1) }), // right
                new THREE.MeshStandardMaterial({ map: createDiceFace(2) }), // left
                new THREE.MeshStandardMaterial({ map: createDiceFace(3) }), // top
                new THREE.MeshStandardMaterial({ map: createDiceFace(4) }), // bottom
                new THREE.MeshStandardMaterial({ map: createDiceFace(5) }), // front
                new THREE.MeshStandardMaterial({ map: createDiceFace(6) }), // back
            ];

            const dice = new THREE.Mesh(diceGeometry, diceMaterials);
            dice.castShadow = true;
            dice.receiveShadow = true;
            dice.position.y = 0;
            scene.add(dice);

            // Physics variables
            let velocity = new THREE.Vector3(0, 0, 0);
            let angularVelocity = new THREE.Vector3(0, 0, 0);
            let isRolling = false;
            let canRoll = true;

            // Mouse/Touch interaction
            let isDragging = false;
            let startPos = { x: 0, y: 0 };
            let endPos = { x: 0, y: 0 };
            let startTime = 0;

            function getEventPos(e) {
                if (e.touches) {
                    return { x: e.touches[0].clientX, y: e.touches[0].clientY };
                }
                return { x: e.clientX, y: e.clientY };
            }

            function onStart(e) {
                if (!canRoll) return;
                isDragging = true;
                const pos = getEventPos(e);
                startPos = pos;
                startTime = Date.now();
            }

            function onEnd(e) {
                if (!isDragging || !canRoll) return;
                isDragging = false;

                const pos = endPos;
                const deltaX = pos.x - startPos.x;
                const deltaY = pos.y - startPos.y;
                const deltaTime = (Date.now() - startTime) / 1000;

                if (Math.abs(deltaX) > 10 || Math.abs(deltaY) > 10) {
                    // Calculate flick velocity
                    const speed =
                        Math.sqrt(deltaX * deltaX + deltaY * deltaY) /
                        deltaTime;
                    const angle = Math.atan2(deltaY, deltaX);

                    velocity.x = Math.cos(angle) * speed * 0.015;
                    velocity.z = Math.sin(angle) * speed * 0.015;
                    velocity.y = Math.min(speed * 0.012, 12);

                    // Random angular velocity
                    angularVelocity.x = (Math.random() - 0.5) * 0.3;
                    angularVelocity.y = (Math.random() - 0.5) * 0.3;
                    angularVelocity.z = (Math.random() - 0.5) * 0.3;

                    isRolling = true;
                    canRoll = false;
                    document.getElementById("result").classList.remove("show");
                }
            }

            function onMove(e) {
                if (isDragging) {
                    endPos = getEventPos(e);
                }
            }

            // Add event listeners
            renderer.domElement.addEventListener("mousedown", onStart);
            renderer.domElement.addEventListener("mouseup", onEnd);
            renderer.domElement.addEventListener("mousemove", onMove);
            renderer.domElement.addEventListener("touchstart", onStart);
            renderer.domElement.addEventListener("touchend", onEnd);
            renderer.domElement.addEventListener("touchmove", onMove);

            // Fireworks particles
            const fireworks = [];
            class Firework {
                constructor() {
                    this.particles = [];
                    this.origin = new THREE.Vector3(
                        (Math.random() - 0.5) * 20,
                        Math.random() * 10 + 5,
                        (Math.random() - 0.5) * 20,
                    );

                    const particleCount = 100;
                    const geometry = new THREE.BufferGeometry();
                    const positions = new Float32Array(particleCount * 3);
                    const colors = new Float32Array(particleCount * 3);
                    const velocities = [];

                    const color = new THREE.Color();
                    color.setHSL(Math.random(), 1, 0.5);

                    for (let i = 0; i < particleCount; i++) {
                        positions[i * 3] = this.origin.x;
                        positions[i * 3 + 1] = this.origin.y;
                        positions[i * 3 + 2] = this.origin.z;

                        colors[i * 3] = color.r;
                        colors[i * 3 + 1] = color.g;
                        colors[i * 3 + 2] = color.b;

                        const velocity = new THREE.Vector3(
                            (Math.random() - 0.5) * 0.5,
                            Math.random() * 0.3 + 0.1,
                            (Math.random() - 0.5) * 0.5,
                        );
                        velocities.push(velocity);
                    }

                    geometry.setAttribute(
                        "position",
                        new THREE.BufferAttribute(positions, 3),
                    );
                    geometry.setAttribute(
                        "color",
                        new THREE.BufferAttribute(colors, 3),
                    );

                    const material = new THREE.PointsMaterial({
                        size: 0.2,
                        vertexColors: true,
                        transparent: true,
                        opacity: 1,
                    });

                    this.mesh = new THREE.Points(geometry, material);
                    this.velocities = velocities;
                    this.life = 1.0;
                    scene.add(this.mesh);
                }

                update(delta) {
                    this.life -= delta * 0.5;
                    this.mesh.material.opacity = this.life;

                    const positions =
                        this.mesh.geometry.attributes.position.array;
                    for (let i = 0; i < this.velocities.length; i++) {
                        positions[i * 3] += this.velocities[i].x;
                        positions[i * 3 + 1] += this.velocities[i].y;
                        positions[i * 3 + 2] += this.velocities[i].z;

                        this.velocities[i].y -= delta * 0.2; // gravity
                    }
                    this.mesh.geometry.attributes.position.needsUpdate = true;

                    if (this.life <= 0) {
                        scene.remove(this.mesh);
                        return false;
                    }
                    return true;
                }
            }

            // Get dice result
            function getDiceResult() {
                const eps = 0.1;
                const sides = {
                    1: new THREE.Vector3(1, 0, 0),
                    2: new THREE.Vector3(-1, 0, 0),
                    3: new THREE.Vector3(0, 1, 0),
                    4: new THREE.Vector3(0, -1, 0),
                    5: new THREE.Vector3(0, 0, 1),
                    6: new THREE.Vector3(0, 0, -1),
                };

                const up = new THREE.Vector3(0, 1, 0);
                let maxDot = -1;
                let result = 1;

                for (let [num, normal] of Object.entries(sides)) {
                    const worldNormal = normal
                        .clone()
                        .applyQuaternion(dice.quaternion);
                    const dot = worldNormal.dot(up);
                    if (dot > maxDot) {
                        maxDot = dot;
                        result = parseInt(num);
                    }
                }

                return result;
            }

            // Animation loop
            const clock = new THREE.Clock();
            function animate() {
                requestAnimationFrame(animate);
                const delta = clock.getDelta();

                if (isRolling) {
                    // Apply physics
                    velocity.y -= 30 * delta; // gravity

                    dice.position.add(
                        velocity.clone().multiplyScalar(delta * 60),
                    );
                    dice.rotation.x += angularVelocity.x;
                    dice.rotation.y += angularVelocity.y;
                    dice.rotation.z += angularVelocity.z;

                    // Ground collision
                    if (dice.position.y <= 0) {
                        dice.position.y = 0;
                        velocity.y *= -0.5; // bounce
                        velocity.x *= 0.8; // friction
                        velocity.z *= 0.8;
                        angularVelocity.multiplyScalar(0.8);

                        // Check if dice has stopped
                        if (
                            Math.abs(velocity.y) < 0.1 &&
                            Math.abs(velocity.x) < 0.1 &&
                            Math.abs(velocity.z) < 0.1
                        ) {
                            isRolling = false;

                            // Snap to grid
                            dice.rotation.x =
                                Math.round(dice.rotation.x / (Math.PI / 2)) *
                                (Math.PI / 2);
                            dice.rotation.y =
                                Math.round(dice.rotation.y / (Math.PI / 2)) *
                                (Math.PI / 2);
                            dice.rotation.z =
                                Math.round(dice.rotation.z / (Math.PI / 2)) *
                                (Math.PI / 2);

                            // Get result
                            const result = getDiceResult();
                            const resultEl = document.getElementById("result");
                            resultEl.textContent = result;
                            resultEl.classList.add("show");

                            // Fireworks for 6
                            if (result === 6) {
                                for (let i = 0; i < 5; i++) {
                                    setTimeout(() => {
                                        fireworks.push(new Firework());
                                    }, i * 200);
                                }
                            }

                            // Allow rolling again after 2 seconds
                            setTimeout(() => {
                                canRoll = true;
                            }, 2000);
                        }
                    }

                    // Keep dice in bounds with wall collisions
                    const wallBounds = 14; // Half of wall length minus dice size

                    // X-axis wall collisions
                    if (Math.abs(dice.position.x) > wallBounds) {
                        dice.position.x =
                            Math.sign(dice.position.x) * wallBounds;
                        velocity.x *= -0.7; // Bounce off wall
                        angularVelocity.y += velocity.x * 0.1; // Add spin from impact
                    }

                    // Z-axis wall collisions
                    if (Math.abs(dice.position.z) > wallBounds) {
                        dice.position.z =
                            Math.sign(dice.position.z) * wallBounds;
                        velocity.z *= -0.7; // Bounce off wall
                        angularVelocity.x += velocity.z * 0.1; // Add spin from impact
                    }
                } else if (!isRolling && canRoll) {
                    // Gentle rotation when idle
                    dice.rotation.x += 0.005;
                    dice.rotation.y += 0.005;
                }

                // Update fireworks
                for (let i = fireworks.length - 1; i >= 0; i--) {
                    if (!fireworks[i].update(delta)) {
                        fireworks.splice(i, 1);
                    }
                }

                renderer.render(scene, camera);
            }

            animate();

            // Window resize
            window.addEventListener("resize", () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            });
        </script>
    </body>
</html>
